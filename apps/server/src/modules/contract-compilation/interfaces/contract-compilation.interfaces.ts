import type {
  CompileContractRequest,
  CompileContractResponseDto,
  CompilationResult,
  ContractMetadata,
  GenerateSourceResponseDto,
  TemplateConfig,
} from '../dto/contract-metadata.dto';

// Service interfaces
export interface IMoveCompilerService {
  /**
   * Compiles Move source code to bytecode
   */
  compile(sourceCode: string, dependencies: string[]): Promise<CompilationResult>;
  
  /**
   * Validates Move source code syntax
   */
  validateSource(sourceCode: string): Promise<boolean>;
  
  /**
   * Gets available compiler version
   */
  getCompilerVersion(): Promise<string>;
  
  /**
   * Checks if compiler is available and working
   */
  isAvailable(): Promise<boolean>;
}

export interface IContractTemplateService {
  /**
   * Generates Move source code from metadata
   */
  generateSource(metadata: ContractMetadata): Promise<string>;
  
  /**
   * Gets template configuration
   */
  getTemplateConfig(version?: string): Promise<TemplateConfig>;
  
  /**
   * Validates metadata against template requirements
   */
  validateMetadata(metadata: ContractMetadata): Promise<{ valid: boolean; errors: string[] }>;
  
  /**
   * Generates unique module name for the contract
   */
  generateModuleName(symbol: string): string;
}

// Use case interfaces
export interface ICompileContractUseCase {
  /**
   * Compiles a contract from metadata
   */
  execute(request: CompileContractRequest): Promise<CompileContractResponseDto>;
}

export interface IGenerateContractUseCase {
  /**
   * Generates Move source code from metadata
   */
  execute(metadata: ContractMetadata): Promise<GenerateSourceResponseDto>;
}

// Configuration interfaces
export interface CompilerConfig {
  moveCliPath?: string;
  tempDirectory: string;
  timeout: number; // Compilation timeout in milliseconds
  maxConcurrentCompilations: number;
}

export interface NetworkConfig {
  name: string;
  rpcUrl: string;
  packageIds: {
    hopfun: string;
    configRegistry: string;
  };
}

// Error types
export interface CompilationErrorDetails {
  stage: 'generation' | 'compilation' | 'validation';
  sourceCode?: string;
  compilerOutput?: string;
  line?: number;
  column?: number;
}

// Metrics and monitoring
export interface CompilationMetrics {
  compilationId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  errorCode?: string;
  metadata: {
    symbol: string;
    network: string;
    templateVersion: string;
  };
}
