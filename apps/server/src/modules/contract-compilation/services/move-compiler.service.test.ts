import { exec } from 'child_process';
import fs from 'fs/promises';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { MoveCompilerService } from './move-compiler.service';

// Mock external dependencies
vi.mock('child_process');
vi.mock('fs/promises');

const mockExec = vi.mocked(exec);
const mockFs = vi.mocked(fs);

describe('MoveCompilerService', () => {
  let service: MoveCompilerService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new MoveCompilerService({
      tempDirectory: '/tmp/test-compilations',
      timeout: 30000,
      maxConcurrentCompilations: 2,
    });
  });

  describe('validateSource', () => {
    it('should validate correct Move source code', async () => {
      const validSource = `
        module coin_template::test {
          public struct TEST has drop {}
          fun init(witness: TEST, ctx: &mut sui::tx_context::TxContext) {
            // Implementation
          }
        }
      `;

      const isValid = await service.validateSource(validSource);
      expect(isValid).toBe(true);
    });

    it('should reject invalid Move source code', async () => {
      const invalidSource = 'not valid move code';

      const isValid = await service.validateSource(invalidSource);
      expect(isValid).toBe(false);
    });

    it('should reject source without required structures', async () => {
      const incompleteSource = `
        module coin_template::test {
          // Missing struct and init function
        }
      `;

      const isValid = await service.validateSource(incompleteSource);
      expect(isValid).toBe(false);
    });
  });

  describe('getCompilerVersion', () => {
    it('should return compiler version', async () => {
      const mockVersion = 'sui 1.0.0';
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: mockVersion, stderr: '' });
        }
        return {} as any;
      });

      const version = await service.getCompilerVersion();
      expect(version).toBe(mockVersion);
    });

    it('should throw error when compiler is not available', async () => {
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(new Error('Command not found'), { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      await expect(service.getCompilerVersion()).rejects.toThrow(
        'Failed to get compiler version',
      );
    });
  });

  describe('isAvailable', () => {
    it('should return true when compiler is available', async () => {
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: 'sui 1.0.0', stderr: '' });
        }
        return {} as any;
      });

      const isAvailable = await service.isAvailable();
      expect(isAvailable).toBe(true);
    });

    it('should return false when compiler is not available', async () => {
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(new Error('Command not found'), { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      const isAvailable = await service.isAvailable();
      expect(isAvailable).toBe(false);
    });
  });

  describe('compile', () => {
    const validSource = `
      module coin_template::test {
        use std::string;
        use sui::coin::{Self, TreasuryCap, CoinMetadata};
        use sui::balance::Balance;
        use hopfun::connector;
        use config_registry::registry::ConfigRegistry;

        public struct TEST has drop {}

        const NAME: vector<u8> = b"Test Token";
        const SYMBOL: vector<u8> = b"TEST";
        const DESCRIPTION: vector<u8> = b"Test Description";
        const ICON_URL: vector<u8> = b"https://example.com/icon.png";
        const TWITTER: vector<u8> = b"https://twitter.com/test";
        const WEBSITE: vector<u8> = b"https://example.com";
        const TELEGRAM: vector<u8> = b"https://t.me/test";
        const TOKEN_DECIMALS: u8 = 6;
        const TOTAL_SUPPLY: u64 = 1000000000000;

        fun init(witness: TEST, ctx: &mut sui::tx_context::TxContext) {
          // Implementation
        }
      }
    `;

    const dependencies = ['0x1::string', '0x2::coin'];

    beforeEach(() => {
      // Mock filesystem operations
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue([
        { name: 'test.mv', isDirectory: () => false } as any,
      ]);
      mockFs.readFile.mockResolvedValue(Buffer.from('mock-bytecode'));
      mockFs.rm.mockResolvedValue(undefined);
    });

    it('should compile valid Move source code', async () => {
      // Mock successful compilation
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: 'Build successful', stderr: '' });
        }
        return {} as any;
      });

      const result = await service.compile(validSource, dependencies);

      expect(result.bytecode).toBeInstanceOf(Uint8Array);
      expect(result.dependencies).toEqual(dependencies);
      expect(result.sourceCode).toBe(validSource);
      expect(result.moduleName).toBe('test');
      expect(result.compilationId).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.name).toBe('Test Token');
      expect(result.metadata.symbol).toBe('TEST');
    });

    it('should throw error when compilation fails', async () => {
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(new Error('Compilation failed'), {
            stdout: '',
            stderr: 'Error details',
          });
        }
        return {} as any;
      });

      await expect(service.compile(validSource, dependencies)).rejects.toThrow(
        'Compilation failed',
      );
    });

    it('should throw error when max concurrent compilations reached', async () => {
      // Start two compilations (max limit)
      const promise1 = service.compile(validSource, dependencies);
      const promise2 = service.compile(validSource, dependencies);

      // Third compilation should fail immediately
      await expect(service.compile(validSource, dependencies)).rejects.toThrow(
        'Maximum concurrent compilations reached',
      );

      // Clean up pending promises
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: 'Build successful', stderr: '' });
        }
        return {} as any;
      });

      await Promise.all([promise1, promise2]);
    });

    it('should handle filesystem errors gracefully', async () => {
      mockFs.mkdir.mockRejectedValue(new Error('Permission denied'));

      await expect(service.compile(validSource, dependencies)).rejects.toThrow(
        'Failed to create temporary project',
      );
    });

    it('should clean up temporary files after compilation', async () => {
      mockExec.mockImplementation((command, options, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: 'Build successful', stderr: '' });
        }
        return {} as any;
      });

      await service.compile(validSource, dependencies);

      // Verify cleanup was called
      expect(mockFs.rm).toHaveBeenCalledWith(
        expect.stringContaining('/tmp/test-compilations/'),
        { recursive: true, force: true },
      );
    });
  });
});
