import { describe, expect, it, beforeEach } from 'vitest';

import type { ContractMetadata } from '../dto/contract-metadata.dto';
import { ContractTemplateService } from './contract-template.service';

describe('ContractTemplateService', () => {
  let service: ContractTemplateService;

  beforeEach(() => {
    service = new ContractTemplateService();
  });

  describe('generateSource', () => {
    it('should generate valid Move source code with correct metadata', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST',
        description: 'A test token',
        imageUrl: 'https://example.com/image.png',
        twitter: 'https://twitter.com/test',
        website: 'https://example.com',
        telegram: 'https://t.me/test',
        totalSupply: 1000000,
        decimals: 6,
      };

      const sourceCode = await service.generateSource(metadata);

      expect(sourceCode).toContain('module coin_template::');
      expect(sourceCode).toContain('const NAME: vector<u8> = b"Test Token"');
      expect(sourceCode).toContain('const SYMBOL: vector<u8> = b"TEST"');
      expect(sourceCode).toContain('const DESCRIPTION: vector<u8> = b"A test token"');
      expect(sourceCode).toContain('const ICON_URL: vector<u8> = b"https://example.com/image.png"');
      expect(sourceCode).toContain('const TWITTER: vector<u8> = b"https://twitter.com/test"');
      expect(sourceCode).toContain('const WEBSITE: vector<u8> = b"https://example.com"');
      expect(sourceCode).toContain('const TELEGRAM: vector<u8> = b"https://t.me/test"');
      expect(sourceCode).toContain('const TOKEN_DECIMALS: u8 = 6');
      expect(sourceCode).toContain('const TOTAL_SUPPLY: u64 = 1000000000000');
    });

    it('should handle empty optional fields', async () => {
      const metadata: ContractMetadata = {
        name: 'Simple Token',
        symbol: 'SIMPLE',
        totalSupply: 1000000,
        decimals: 6,
      };

      const sourceCode = await service.generateSource(metadata);

      expect(sourceCode).toContain('const NAME: vector<u8> = b"Simple Token"');
      expect(sourceCode).toContain('const SYMBOL: vector<u8> = b"SIMPLE"');
      expect(sourceCode).toContain('const DESCRIPTION: vector<u8> = b""');
      expect(sourceCode).toContain('const ICON_URL: vector<u8> = b""');
      expect(sourceCode).toContain('const TWITTER: vector<u8> = b""');
      expect(sourceCode).toContain('const WEBSITE: vector<u8> = b""');
      expect(sourceCode).toContain('const TELEGRAM: vector<u8> = b""');
    });

    it('should escape special characters in strings', async () => {
      const metadata: ContractMetadata = {
        name: 'Token with "quotes"',
        symbol: 'QUOTE',
        description: 'Description with \\ backslash',
        totalSupply: 1000000,
        decimals: 6,
      };

      const sourceCode = await service.generateSource(metadata);

      expect(sourceCode).toContain('const NAME: vector<u8> = b"Token with \\"quotes\\""');
      expect(sourceCode).toContain('const DESCRIPTION: vector<u8> = b"Description with \\\\ backslash"');
    });

    it('should generate unique module names', async () => {
      const metadata1: ContractMetadata = {
        name: 'Token 1',
        symbol: 'TOK1',
        totalSupply: 1000000,
        decimals: 6,
      };

      const metadata2: ContractMetadata = {
        name: 'Token 2',
        symbol: 'TOK2',
        totalSupply: 1000000,
        decimals: 6,
      };

      const sourceCode1 = await service.generateSource(metadata1);
      const sourceCode2 = await service.generateSource(metadata2);

      const moduleName1 = sourceCode1.match(/module coin_template::(\w+)/)?.[1];
      const moduleName2 = sourceCode2.match(/module coin_template::(\w+)/)?.[1];

      expect(moduleName1).toBeDefined();
      expect(moduleName2).toBeDefined();
      expect(moduleName1).not.toBe(moduleName2);
    });

    it('should throw error for invalid metadata', async () => {
      const invalidMetadata = {
        name: 'Test Token',
        symbol: 'TEST',
        totalSupply: -1, // Invalid negative supply
        decimals: 6,
      } as ContractMetadata;

      await expect(service.generateSource(invalidMetadata)).rejects.toThrow();
    });
  });

  describe('validateMetadata', () => {
    it('should validate correct metadata', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST123',
        description: 'A test token',
        imageUrl: 'https://example.com/image.png',
        twitter: 'https://twitter.com/test',
        website: 'https://example.com',
        telegram: 'https://t.me/test',
        totalSupply: 1000000,
        decimals: 6,
      };

      const result = await service.validateMetadata(metadata);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid symbol format', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST-INVALID!',
        totalSupply: 1000000,
        decimals: 6,
      };

      const result = await service.validateMetadata(metadata);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Symbol must contain only alphanumeric characters');
    });

    it('should reject invalid total supply', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST',
        totalSupply: 0,
        decimals: 6,
      };

      const result = await service.validateMetadata(metadata);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Total supply must be positive');
    });

    it('should reject invalid decimals', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST',
        totalSupply: 1000000,
        decimals: 25, // Too high
      };

      const result = await service.validateMetadata(metadata);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Decimals must be between 0 and 18');
    });

    it('should reject invalid URLs', async () => {
      const metadata: ContractMetadata = {
        name: 'Test Token',
        symbol: 'TEST',
        imageUrl: 'not-a-url',
        twitter: 'invalid-twitter',
        website: 'not-a-website',
        telegram: 'invalid-telegram',
        totalSupply: 1000000,
        decimals: 6,
      };

      const result = await service.validateMetadata(metadata);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid image URL format');
      expect(result.errors).toContain('Invalid Twitter URL format');
      expect(result.errors).toContain('Invalid website URL format');
      expect(result.errors).toContain('Invalid Telegram URL format');
    });
  });

  describe('getTemplateConfig', () => {
    it('should return default template config', async () => {
      const config = await service.getTemplateConfig();

      expect(config.version).toBe('latest');
      expect(config.dependencies).toBeInstanceOf(Array);
      expect(config.dependencies.length).toBeGreaterThan(0);
      expect(config.supportedNetworks).toContain('TESTNET');
    });

    it('should return specific version config', async () => {
      const config = await service.getTemplateConfig('v1.0.0');

      expect(config.version).toBe('latest'); // Currently all versions map to latest
      expect(config.dependencies).toBeInstanceOf(Array);
    });

    it('should throw error for unknown version', async () => {
      await expect(service.getTemplateConfig('unknown-version')).rejects.toThrow(
        'Template version \'unknown-version\' not found',
      );
    });
  });

  describe('generateModuleName', () => {
    it('should generate valid module names', () => {
      const name1 = service.generateModuleName('TEST');
      const name2 = service.generateModuleName('ANOTHER');

      expect(name1).toMatch(/^test_[a-f0-9]{8}$/);
      expect(name2).toMatch(/^another_[a-f0-9]{8}$/);
      expect(name1).not.toBe(name2);
    });

    it('should handle special characters in symbol', () => {
      const name = service.generateModuleName('TEST-123!@#');

      expect(name).toMatch(/^test123_[a-f0-9]{8}$/);
    });
  });
});
