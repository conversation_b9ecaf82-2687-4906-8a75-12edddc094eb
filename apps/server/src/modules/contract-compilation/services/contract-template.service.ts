import { logger } from '@hopfun/logger';
import crypto from 'crypto';
import path from 'path';

import type {
  ContractMetadata,
  TemplateConfig,
} from '../dto/contract-metadata.dto';
import type { IContractTemplateService } from '../interfaces/contract-compilation.interfaces';

export class ContractTemplateService implements IContractTemplateService {
  private readonly templateConfigs = new Map<string, TemplateConfig>();

  constructor() {
    this.initializeTemplates();
  }

  private initializeTemplates(): void {
    // Default template configuration
    const defaultTemplate: TemplateConfig = {
      version: 'latest',
      templatePath: path.join(process.cwd(), 'templates', 'coin-template.move'),
      dependencies: [
        '0x0000000000000000000000000000000000000000000000000000000000000001', // MoveStdlib
        '0x0000000000000000000000000000000000000000000000000000000000000002', // Sui Framework
      ],
      supportedNetworks: ['MAINNET', 'TESTNET', 'DEVNET'],
    };

    this.templateConfigs.set('latest', defaultTemplate);
    this.templateConfigs.set('v1.0.0', defaultTemplate);

    logger.info(
      { templateCount: this.templateConfigs.size },
      'Contract templates initialized',
    );
  }

  async generateSource(metadata: ContractMetadata): Promise<string> {
    const startTime = Date.now();

    try {
      logger.debug({ metadata }, 'Generating Move source code');

      // Validate metadata
      const validation = await this.validateMetadata(metadata);
      if (!validation.valid) {
        throw new Error(`Invalid metadata: ${validation.errors.join(', ')}`);
      }

      // Generate unique module name
      const moduleName = this.generateModuleName(metadata.symbol);
      const structName = metadata.symbol
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '');
      const totalSupplyWithDecimals =
        metadata.totalSupply * Math.pow(10, metadata.decimals);
      const tempId = this.generateTempId(metadata);

      // Escape strings for Move
      const escapedName = this.escapeString(metadata.name);
      const escapedSymbol = this.escapeString(metadata.symbol);
      const escapedDescription = this.escapeString(metadata.description || '');
      const escapedImageUrl = this.escapeString(metadata.imageUrl || '');
      const escapedTwitter = this.escapeString(metadata.twitter || '');
      const escapedWebsite = this.escapeString(metadata.website || '');
      const escapedTelegram = this.escapeString(metadata.telegram || '');

      // Generate proper one-time witness name (must be "COIN" for module name::coin format)
      const otwName = 'COIN';

      const sourceCode = `module ${moduleName}::coin {
    use std::option;
    use std::string;
    use std::type_name;
    use sui::coin::{Self, TreasuryCap, CoinMetadata};
    use sui::balance::Balance;
    use sui::event;

    /// The One-Time Witness for the Coin
    public struct ${otwName} has drop {}

    const ICON_URL: vector<u8> = b"${escapedImageUrl}";
    const TOKEN_DECIMALS: u8 = ${metadata.decimals};

    const TWITTER: vector<u8> = b"${escapedTwitter}";
    const WEBSITE: vector<u8> = b"${escapedWebsite}";
    const TELEGRAM: vector<u8> = b"${escapedTelegram}";

    const NAME: vector<u8> = b"${escapedName}";
    const SYMBOL: vector<u8> = b"${escapedSymbol}";
    const DESCRIPTION: vector<u8> = b"${escapedDescription}";

    const TEMP_ID: u64 = ${tempId};

    const TOTAL_SUPPLY: u64 = ${totalSupplyWithDecimals};

    /// Event emitted when a connector is created
    public struct ConnectorCreated has copy, drop {
        connector_id: sui::object::ID,
        coin: std::type_name::TypeName,
    }

    /// Connector struct that matches HopFun's Connector exactly
    /// This will be compatible with accept_connector function
    public struct Connector<phantom T> has key, store {
        id: sui::object::UID,
        temp_id: u64,
        supply: Balance<T>,
        twitter: std::string::String,
        website: std::string::String,
        telegram: std::string::String,
        creator: address,
    }

    /// Holder for currency components until connector is created
    public struct CurrencyHolder<phantom T> has key {
        id: sui::object::UID,
        treasury_cap: TreasuryCap<T>,
        supply: Balance<T>,
        temp_id: u64,
        creator: address,
    }

    /// Init the Coin - Creates currency and stores components for later connector creation
    fun init(witness: ${otwName}, ctx: &mut sui::tx_context::TxContext) {
        // Use the OTW to create the currency immediately
        let (mut treasury_cap, metadata) = coin::create_currency<${otwName}>(
            witness,
            TOKEN_DECIMALS,
            SYMBOL,
            NAME,
            DESCRIPTION,
            option::some(sui::url::new_unsafe_from_bytes(ICON_URL)),
            ctx
        );

        // Freeze the metadata
        sui::transfer::public_freeze_object<CoinMetadata<${otwName}>>(metadata);

        // Mint the total supply
        let supply = coin::mint(&mut treasury_cap, TOTAL_SUPPLY, ctx);
        let creator = sui::tx_context::sender(ctx);

        // Store the treasury cap and supply for later use
        let holder = CurrencyHolder<${otwName}> {
            id: sui::object::new(ctx),
            treasury_cap,
            supply: coin::into_balance(supply),
            temp_id: TEMP_ID,
            creator,
        };

        sui::transfer::transfer(holder, creator);
    }

    /// Create a connector with the supply (second transaction)
    /// This creates a Connector that matches HopFun's Connector structure exactly
    public entry fun create_connector(
        holder: CurrencyHolder<${otwName}>,
        temp_id: u64,
        ctx: &mut sui::tx_context::TxContext
    ) {
        let CurrencyHolder {
            id,
            treasury_cap,
            supply,
            temp_id: _holder_temp_id,
            creator
        } = holder;
        sui::object::delete(id);

        // Freeze the treasury cap
        sui::transfer::public_freeze_object<TreasuryCap<${otwName}>>(treasury_cap);

        // Create a Connector that matches HopFun's structure exactly
        // This will be compatible with accept_connector function
        let connector = Connector<${otwName}> {
            id: sui::object::new(ctx),
            temp_id,
            supply,
            twitter: std::string::utf8(TWITTER),
            website: std::string::utf8(WEBSITE),
            telegram: std::string::utf8(TELEGRAM),
            creator,
        };

        // Emit the ConnectorCreated event that the frontend expects
        sui::event::emit(ConnectorCreated {
            connector_id: sui::object::id(&connector),
            coin: std::type_name::get<${otwName}>(),
        });

        // Transfer the Connector to creator using public_transfer
        // The MemeConfig will receive it when accept_connector is called
        sui::transfer::public_transfer(connector, creator);
    }

    /// Extract supply and metadata for external HopFun Connector creation
    /// This provides the data needed to create a HopFun Connector externally
    public entry fun extract_supply_data(
        holder: CurrencyHolder<${otwName}>,
        ctx: &mut sui::tx_context::TxContext
    ) {
        let CurrencyHolder {
            id,
            treasury_cap,
            supply,
            temp_id,
            creator
        } = holder;
        sui::object::delete(id);

        // Freeze the treasury cap
        sui::transfer::public_freeze_object<TreasuryCap<${otwName}>>(treasury_cap);

        // Create a data holder that can be used to create HopFun Connector externally
        let supply_data = SupplyData<${otwName}> {
            id: sui::object::new(ctx),
            supply,
            temp_id,
            twitter: std::string::utf8(TWITTER),
            website: std::string::utf8(WEBSITE),
            telegram: std::string::utf8(TELEGRAM),
            creator,
        };

        // Emit event with the supply data
        sui::event::emit(SupplyDataCreated {
            supply_data_id: sui::object::id(&supply_data),
            coin: std::type_name::get<${otwName}>(),
            temp_id,
            creator,
        });

        // Transfer the supply data to creator for HopFun Connector creation
        sui::transfer::public_transfer(supply_data, creator);
    }

    /// Data holder for supply and metadata
    public struct SupplyData<phantom T> has key, store {
        id: sui::object::UID,
        supply: Balance<T>,
        temp_id: u64,
        twitter: std::string::String,
        website: std::string::String,
        telegram: std::string::String,
        creator: address,
    }

    /// Event emitted when supply data is created
    public struct SupplyDataCreated has copy, drop {
        supply_data_id: sui::object::ID,
        coin: std::type_name::TypeName,
        temp_id: u64,
        creator: address,
    }
}`;

      const duration = Date.now() - startTime;
      logger.info(
        {
          moduleName,
          structName,
          symbol: metadata.symbol,
          duration,
        },
        'Move source code generated successfully',
      );

      return sourceCode;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          error: errorMessage,
          metadata: metadata.symbol,
          duration,
        },
        'Failed to generate Move source code',
      );

      throw error;
    }
  }

  async getTemplateConfig(version = 'latest'): Promise<TemplateConfig> {
    const config = this.templateConfigs.get(version);
    if (!config) {
      throw new Error(`Template version '${version}' not found`);
    }
    return { ...config }; // Return a copy
  }

  async validateMetadata(
    metadata: ContractMetadata,
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Validate symbol format (alphanumeric only)
    if (!/^[A-Z0-9]+$/i.test(metadata.symbol)) {
      errors.push('Symbol must contain only alphanumeric characters');
    }

    // Validate total supply
    if (metadata.totalSupply <= 0) {
      errors.push('Total supply must be positive');
    }

    // Validate decimals
    if (metadata.decimals < 0 || metadata.decimals > 18) {
      errors.push('Decimals must be between 0 and 18');
    }

    // Validate URLs if provided
    if (metadata.imageUrl && !this.isValidUrl(metadata.imageUrl)) {
      errors.push('Invalid image URL format');
    }

    if (metadata.twitter && !this.isValidUrl(metadata.twitter)) {
      errors.push('Invalid Twitter URL format');
    }

    if (metadata.website && !this.isValidUrl(metadata.website)) {
      errors.push('Invalid website URL format');
    }

    if (metadata.telegram && !this.isValidUrl(metadata.telegram)) {
      errors.push('Invalid Telegram URL format');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  generateModuleName(symbol: string): string {
    // Generate a deterministic but unique module name
    const hash = crypto
      .createHash('md5')
      .update(symbol + Date.now())
      .digest('hex');
    const cleanSymbol = symbol.toLowerCase().replace(/[^a-z0-9]/g, '');
    return `${cleanSymbol}_${hash.substring(0, 8)}`;
  }

  private generateTempId(metadata: ContractMetadata): number {
    // Generate a deterministic temp ID based on metadata
    const hash = crypto
      .createHash('md5')
      .update(metadata.symbol + metadata.name + Date.now())
      .digest('hex');
    return parseInt(hash.substring(0, 8), 16) % 1000000;
  }

  private escapeString(str: string): string {
    return str.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
