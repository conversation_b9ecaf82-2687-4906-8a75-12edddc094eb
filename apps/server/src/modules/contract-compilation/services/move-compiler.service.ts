import { logger } from '@hopfun/logger';
import { exec } from 'child_process';
import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';
import { promisify } from 'util';

import type {
  CompilationResult,
  ContractMetadata,
} from '../dto/contract-metadata.dto';
import type {
  CompilerConfig,
  IMoveCompilerService,
} from '../interfaces/contract-compilation.interfaces';

const execAsync = promisify(exec);

export class MoveCompilerService implements IMoveCompilerService {
  private readonly config: CompilerConfig;
  private readonly activeCompilations = new Set<string>();

  constructor(config?: Partial<CompilerConfig>) {
    this.config = {
      moveCliPath: config?.moveCliPath || 'sui',
      tempDirectory:
        config?.tempDirectory ||
        path.join(process.cwd(), 'temp', 'compilations'),
      timeout: config?.timeout || 60000, // 60 seconds
      maxConcurrentCompilations: config?.maxConcurrentCompilations || 5,
    };

    this.ensureTempDirectory();
    logger.info(this.config, 'Move compiler service initialized');
  }

  async compile(
    sourceCode: string,
    dependencies: string[],
  ): Promise<CompilationResult> {
    const compilationId = this.generateCompilationId();
    const startTime = Date.now();

    // Check concurrent compilation limit
    if (this.activeCompilations.size >= this.config.maxConcurrentCompilations) {
      throw new Error(
        'Maximum concurrent compilations reached. Please try again later.',
      );
    }

    this.activeCompilations.add(compilationId);

    try {
      logger.info(
        { compilationId, activeCompilations: this.activeCompilations.size },
        'Starting Move compilation',
      );

      // Create temporary project directory
      const projectDir = await this.createTempProject(
        compilationId,
        sourceCode,
        dependencies,
      );

      // Compile the Move package
      const bytecode = await this.compileProject(projectDir);

      // Extract metadata from source code (this is a simplified approach)
      const metadata = this.extractMetadataFromSource(sourceCode);

      const result: CompilationResult = {
        bytecode,
        dependencies,
        sourceCode,
        moduleName: this.extractModuleName(sourceCode),
        metadata,
        compilationId,
      };

      const duration = Date.now() - startTime;
      logger.info(
        {
          compilationId,
          duration,
          bytecodeSize: bytecode.length,
        },
        'Move compilation completed successfully',
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          compilationId,
          error: errorMessage,
          duration,
        },
        'Move compilation failed',
      );

      throw new Error(`Compilation failed: ${errorMessage}`);
    } finally {
      this.activeCompilations.delete(compilationId);
      // Clean up temp directory (fire and forget)
      this.cleanupTempProject(compilationId).catch((error) => {
        logger.warn({ compilationId, error }, 'Failed to cleanup temp project');
      });
    }
  }

  async validateSource(sourceCode: string): Promise<boolean> {
    try {
      logger.debug('Validating Move source code syntax');

      // Basic syntax validation
      if (
        !sourceCode.includes('module ') ||
        !sourceCode.includes('fun init(')
      ) {
        return false;
      }

      // Check for required structures
      const requiredPatterns = [
        /module\s+(0x\w+::\w+|\w+::\w+|\w+)\s*{/, // Support "module 0x0::name", "module addr::name", "module name"
        /public\s+struct\s+\w+\s+has\s+drop\s*{}/,
        /fun\s+init\(/,
      ];

      for (const pattern of requiredPatterns) {
        if (!pattern.test(sourceCode)) {
          logger.debug({ pattern: pattern.source }, 'Source validation failed');
          return false;
        }
      }

      logger.debug('Move source code validation passed');
      return true;
    } catch (error) {
      logger.error({ error }, 'Source validation error');
      return false;
    }
  }

  async getCompilerVersion(): Promise<string> {
    try {
      const { stdout } = await execAsync(
        `${this.config.moveCliPath} --version`,
        {
          timeout: 5000,
        },
      );
      return stdout.trim();
    } catch (error) {
      logger.error({ error }, 'Failed to get compiler version');
      throw new Error('Failed to get compiler version');
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.getCompilerVersion();
      return true;
    } catch {
      return false;
    }
  }

  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.config.tempDirectory, { recursive: true });
    } catch (error) {
      logger.error({ error }, 'Failed to create temp directory');
      throw new Error('Failed to initialize compiler temp directory');
    }
  }

  private generateCompilationId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  private async createTempProject(
    compilationId: string,
    sourceCode: string,
    dependencies: string[],
  ): Promise<string> {
    const projectDir = path.join(this.config.tempDirectory, compilationId);

    try {
      logger.debug(
        { compilationId, projectDir },
        'Creating temp project directory',
      );

      // Create project structure
      await fs.mkdir(projectDir, { recursive: true });
      await fs.mkdir(path.join(projectDir, 'sources'), { recursive: true });

      // Write source file
      const moduleName = this.extractModuleName(sourceCode);
      logger.debug({ compilationId, moduleName }, 'Extracted module name');

      // Write Move.toml
      const moveToml = this.generateMoveToml(moduleName);
      logger.debug(
        { compilationId, moveTomlLength: moveToml.length },
        'Generated Move.toml',
      );

      await fs.writeFile(path.join(projectDir, 'Move.toml'), moveToml);
      logger.debug({ compilationId }, 'Wrote Move.toml file');

      await fs.writeFile(
        path.join(projectDir, 'sources', `${moduleName}.move`),
        sourceCode,
      );
      logger.debug({ compilationId }, 'Wrote source file');

      return projectDir;
    } catch (error) {
      logger.error(
        {
          compilationId,
          error:
            error instanceof Error
              ? {
                  message: error.message,
                  stack: error.stack,
                  name: error.name,
                }
              : error,
          projectDir,
        },
        'Failed to create temp project',
      );
      throw new Error('Failed to create temporary project');
    }
  }

  private async compileProject(projectDir: string): Promise<Uint8Array> {
    try {
      const buildDir = path.join(projectDir, 'build');

      // Run sui move build
      const { stdout, stderr } = await execAsync(
        `${this.config.moveCliPath} move build --path ${projectDir}`,
        {
          timeout: this.config.timeout,
          cwd: projectDir,
        },
      );

      logger.debug({ stdout, stderr }, 'Move build output');

      // Find the compiled bytecode file
      const bytecodeFiles = await this.findBytecodeFiles(buildDir);
      if (bytecodeFiles.length === 0) {
        throw new Error('No bytecode files found after compilation');
      }

      // Read the first bytecode file (assuming single module)
      const bytecodeFile = bytecodeFiles[0];
      const bytecodeBuffer = await fs.readFile(bytecodeFile);

      return new Uint8Array(bytecodeBuffer);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { projectDir, error: errorMessage },
        'Project compilation failed',
      );
      throw new Error(`Build failed: ${errorMessage}`);
    }
  }

  private async findBytecodeFiles(buildDir: string): Promise<string[]> {
    const bytecodeFiles: string[] = [];

    try {
      const entries = await fs.readdir(buildDir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          const subDir = path.join(buildDir, entry.name);
          const subFiles = await this.findBytecodeFiles(subDir);
          bytecodeFiles.push(...subFiles);
        } else if (entry.name.endsWith('.mv')) {
          bytecodeFiles.push(path.join(buildDir, entry.name));
        }
      }
    } catch (error) {
      logger.debug({ buildDir, error }, 'Error reading build directory');
    }

    return bytecodeFiles;
  }

  private generateMoveToml(moduleName: string): string {
    return `[package]
name = "DynamicCoin"
version = "0.0.1"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }
MoveStdlib = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/move-stdlib", rev = "framework/devnet" }

[addresses]
${moduleName} = "0x0"
`;
  }

  private extractModuleName(sourceCode: string): string {
    // Handle formats: "module name::coin", "module 0x0::name", "module name {", "module coin_template::name"
    const newFormatMatch = /module\s+(\w+)::coin\s*{/.exec(sourceCode);
    if (newFormatMatch) {
      return newFormatMatch[1]; // Return the address part (e.g., "testaddr_123" from "module testaddr_123::coin")
    }

    const addressModuleMatch = /module\s+0x\w+::(\w+)\s*{/.exec(sourceCode);
    if (addressModuleMatch) {
      return addressModuleMatch[1];
    }

    const simpleFormatMatch = /module\s+(\w+)\s*{/.exec(sourceCode);
    if (simpleFormatMatch) {
      return simpleFormatMatch[1];
    }

    const oldFormatMatch = /module\s+\w+::(\w+)\s*{/.exec(sourceCode);
    return oldFormatMatch ? oldFormatMatch[1] : 'template';
  }

  private extractMetadataFromSource(sourceCode: string): ContractMetadata {
    // This is a simplified extraction - in a real implementation,
    // you might want to parse the AST or use more sophisticated methods
    const extractConstant = (name: string): string => {
      const regex = new RegExp(
        `const\\s+${name}:\\s*vector<u8>\\s*=\\s*b"([^"]*)"`,
        'i',
      );
      const match = sourceCode.match(regex);
      return match ? match[1] : '';
    };

    const extractNumber = (name: string): number => {
      const regex = new RegExp(
        `const\\s+${name}:\\s*u\\d+\\s*=\\s*(\\d+)`,
        'i',
      );
      const match = sourceCode.match(regex);
      return match ? parseInt(match[1], 10) : 0;
    };

    return {
      name: extractConstant('NAME'),
      symbol: extractConstant('SYMBOL'),
      description: extractConstant('DESCRIPTION'),
      imageUrl: extractConstant('ICON_URL'),
      twitter: extractConstant('TWITTER'),
      website: extractConstant('WEBSITE'),
      telegram: extractConstant('TELEGRAM'),
      totalSupply: extractNumber('TOTAL_SUPPLY'),
      decimals: extractNumber('TOKEN_DECIMALS'),
    };
  }

  private async cleanupTempProject(compilationId: string): Promise<void> {
    const projectDir = path.join(this.config.tempDirectory, compilationId);

    try {
      await fs.rm(projectDir, { recursive: true, force: true });
      logger.debug({ compilationId }, 'Temp project cleaned up');
    } catch (error) {
      logger.warn({ compilationId, error }, 'Failed to cleanup temp project');
    }
  }
}
