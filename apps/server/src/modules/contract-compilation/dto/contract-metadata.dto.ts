import { z } from 'zod';

// Input validation schemas
export const ContractMetadataSchema = z.object({
  name: z
    .string()
    .min(1, 'Token name is required')
    .max(100, 'Token name too long'),
  symbol: z
    .string()
    .min(1, 'Token symbol is required')
    .max(20, 'Token symbol too long'),
  description: z.string().max(500, 'Description too long').optional(),
  imageUrl: z.string().optional(),
  twitter: z.string().optional(),
  website: z.string().optional(),
  telegram: z.string().optional(),
  totalSupply: z.number().min(1, 'Total supply must be positive'),
  decimals: z
    .number()
    .min(0)
    .max(18, 'Decimals must be between 0 and 18')
    .default(6),
});

export const CompileContractRequestSchema = z.object({
  metadata: ContractMetadataSchema,
  network: z.enum(['MAINNET', 'TESTNET', 'DEVNET']).default('TESTNET'),
  templateVersion: z.string().optional().default('latest'),
});

// Type definitions
export type ContractMetadata = z.infer<typeof ContractMetadataSchema>;
export type CompileContractRequest = z.infer<
  typeof CompileContractRequestSchema
>;

// Response DTOs
export interface CompileContractResponseDto {
  success: boolean;
  data?: {
    bytecode: string; // Base64 encoded bytecode
    dependencies: string[]; // Package dependencies
    sourceCode: string; // Generated Move source code
    moduleName: string; // Generated module name
    packageId?: string; // If deployed
    metadata: ContractMetadata;
    compilationId: string; // Unique ID for this compilation
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

export interface GenerateSourceResponseDto {
  success: boolean;
  data?: {
    sourceCode: string;
    moduleName: string;
    metadata: ContractMetadata;
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

// Internal types for service layer
export interface CompilationResult {
  bytecode: Uint8Array;
  dependencies: string[];
  sourceCode: string;
  moduleName: string;
  metadata: ContractMetadata;
  compilationId: string;
}

export interface CompilationError extends Error {
  code: string;
  details?: unknown;
}

// Template configuration
export interface TemplateConfig {
  version: string;
  templatePath: string;
  dependencies: string[];
  supportedNetworks: string[];
}
