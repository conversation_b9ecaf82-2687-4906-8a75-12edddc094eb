import { logger } from '@hopfun/logger';

import type {
  ContractMetadata,
  GenerateSourceResponseDto,
} from '../dto/contract-metadata.dto';
import type {
  IContractTemplateService,
  IGenerateContractUseCase,
} from '../interfaces/contract-compilation.interfaces';
import { ContractTemplateService } from '../services/contract-template.service';

export class GenerateContractUseCase implements IGenerateContractUseCase {
  private templateService: IContractTemplateService;

  constructor(templateService?: IContractTemplateService) {
    this.templateService = templateService ?? new ContractTemplateService();
  }

  async execute(metadata: ContractMetadata): Promise<GenerateSourceResponseDto> {
    const startTime = Date.now();

    logger.info(
      {
        symbol: metadata.symbol,
        name: metadata.name,
      },
      'Starting contract source generation',
    );

    try {
      // Step 1: Validate metadata
      const validation = await this.templateService.validateMetadata(metadata);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            code: 'INVALID_METADATA',
            message: 'Contract metadata validation failed',
            details: { errors: validation.errors },
          },
        };
      }

      // Step 2: Generate source code
      logger.debug({ symbol: metadata.symbol }, 'Generating Move source code');
      const sourceCode = await this.templateService.generateSource(metadata);

      // Step 3: Generate module name
      const moduleName = this.templateService.generateModuleName(metadata.symbol);

      const duration = Date.now() - startTime;
      logger.info(
        {
          symbol: metadata.symbol,
          moduleName,
          sourceCodeLength: sourceCode.length,
          duration,
        },
        'Contract source generation completed successfully',
      );

      return {
        success: true,
        data: {
          sourceCode,
          moduleName,
          metadata,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error(
        {
          symbol: metadata.symbol,
          error: errorMessage,
          duration,
        },
        'Contract source generation failed',
      );

      return {
        success: false,
        error: {
          code: 'GENERATION_FAILED',
          message: `Source code generation failed: ${errorMessage}`,
          details: {
            originalError: errorMessage,
            duration,
          },
        },
      };
    }
  }
}
