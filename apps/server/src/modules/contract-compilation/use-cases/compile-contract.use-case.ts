import { logger } from '@hopfun/logger';
import crypto from 'crypto';

import type {
  CompileContractRequest,
  CompileContractResponseDto,
} from '../dto/contract-metadata.dto';
import type {
  ICompileContractUseCase,
  IContractTemplateService,
  IMoveCompilerService,
} from '../interfaces/contract-compilation.interfaces';
import { ContractTemplateService } from '../services/contract-template.service';
import { MoveCompilerService } from '../services/move-compiler.service';

export class CompileContractUseCase implements ICompileContractUseCase {
  private templateService: IContractTemplateService;
  private compilerService: IMoveCompilerService;

  constructor(
    templateService?: IContractTemplateService,
    compilerService?: IMoveCompilerService,
  ) {
    this.templateService = templateService ?? new ContractTemplateService();
    this.compilerService = compilerService ?? new MoveCompilerService();
  }

  async execute(request: CompileContractRequest): Promise<CompileContractResponseDto> {
    const startTime = Date.now();
    const compilationId = this.generateCompilationId();

    logger.info(
      {
        compilationId,
        symbol: request.metadata.symbol,
        network: request.network,
        templateVersion: request.templateVersion,
      },
      'Starting contract compilation',
    );

    try {
      // Step 1: Validate metadata
      const validation = await this.templateService.validateMetadata(request.metadata);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            code: 'INVALID_METADATA',
            message: 'Contract metadata validation failed',
            details: { errors: validation.errors },
          },
        };
      }

      // Step 2: Check compiler availability
      const isCompilerAvailable = await this.compilerService.isAvailable();
      if (!isCompilerAvailable) {
        return {
          success: false,
          error: {
            code: 'COMPILER_UNAVAILABLE',
            message: 'Move compiler is not available',
            details: { 
              suggestion: 'Please ensure Sui CLI is installed and accessible',
            },
          },
        };
      }

      // Step 3: Generate source code
      logger.debug({ compilationId }, 'Generating Move source code');
      const sourceCode = await this.templateService.generateSource(request.metadata);

      // Step 4: Validate generated source
      const isValidSource = await this.compilerService.validateSource(sourceCode);
      if (!isValidSource) {
        return {
          success: false,
          error: {
            code: 'INVALID_SOURCE_CODE',
            message: 'Generated source code is invalid',
            details: { sourceCode },
          },
        };
      }

      // Step 5: Get template configuration for dependencies
      const templateConfig = await this.templateService.getTemplateConfig(
        request.templateVersion,
      );

      // Step 6: Compile the contract
      logger.debug({ compilationId }, 'Compiling Move contract');
      const compilationResult = await this.compilerService.compile(
        sourceCode,
        templateConfig.dependencies,
      );

      // Step 7: Convert bytecode to base64
      const bytecodeBase64 = Buffer.from(compilationResult.bytecode).toString('base64');

      const duration = Date.now() - startTime;
      logger.info(
        {
          compilationId,
          symbol: request.metadata.symbol,
          moduleName: compilationResult.moduleName,
          bytecodeSize: compilationResult.bytecode.length,
          duration,
        },
        'Contract compilation completed successfully',
      );

      return {
        success: true,
        data: {
          bytecode: bytecodeBase64,
          dependencies: compilationResult.dependencies,
          sourceCode: compilationResult.sourceCode,
          moduleName: compilationResult.moduleName,
          metadata: request.metadata,
          compilationId,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error(
        {
          compilationId,
          symbol: request.metadata.symbol,
          error: errorMessage,
          duration,
        },
        'Contract compilation failed',
      );

      // Determine error code based on error type
      let errorCode = 'COMPILATION_FAILED';
      if (errorMessage.includes('timeout')) {
        errorCode = 'COMPILATION_TIMEOUT';
      } else if (errorMessage.includes('syntax')) {
        errorCode = 'SYNTAX_ERROR';
      } else if (errorMessage.includes('dependency')) {
        errorCode = 'DEPENDENCY_ERROR';
      }

      return {
        success: false,
        error: {
          code: errorCode,
          message: `Contract compilation failed: ${errorMessage}`,
          details: {
            compilationId,
            originalError: errorMessage,
            duration,
          },
        },
      };
    }
  }

  private generateCompilationId(): string {
    return crypto.randomBytes(16).toString('hex');
  }
}
