import { describe, expect, it, beforeEach, vi } from 'vitest';

import type {
  CompileContractRequest,
  ContractMetadata,
} from '../dto/contract-metadata.dto';
import type {
  IContractTemplateService,
  IMoveCompilerService,
} from '../interfaces/contract-compilation.interfaces';
import { CompileContractUseCase } from './compile-contract.use-case';

// Mock services
const mockTemplateService: IContractTemplateService = {
  generateSource: vi.fn(),
  getTemplateConfig: vi.fn(),
  validateMetadata: vi.fn(),
  generateModuleName: vi.fn(),
};

const mockCompilerService: IMoveCompilerService = {
  compile: vi.fn(),
  validateSource: vi.fn(),
  getCompilerVersion: vi.fn(),
  isAvailable: vi.fn(),
};

describe('CompileContractUseCase', () => {
  let useCase: CompileContractUseCase;

  beforeEach(() => {
    vi.clearAllMocks();
    useCase = new CompileContractUseCase(mockTemplateService, mockCompilerService);
  });

  const validMetadata: ContractMetadata = {
    name: 'Test Token',
    symbol: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: 'https://twitter.com/test',
    website: 'https://example.com',
    telegram: 'https://t.me/test',
    totalSupply: 1000000,
    decimals: 6,
  };

  const validRequest: CompileContractRequest = {
    metadata: validMetadata,
    network: 'TESTNET',
    templateVersion: 'latest',
  };

  describe('execute', () => {
    it('should successfully compile a valid contract', async () => {
      const mockSourceCode = 'module coin_template::test { /* mock source */ }';
      const mockBytecode = new Uint8Array([1, 2, 3, 4]);
      const mockDependencies = ['0x1::string', '0x2::coin'];

      // Setup mocks
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: mockDependencies,
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockResolvedValue({
        bytecode: mockBytecode,
        dependencies: mockDependencies,
        sourceCode: mockSourceCode,
        moduleName: 'test',
        metadata: validMetadata,
        compilationId: 'test-compilation-id',
      });

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.bytecode).toBe(Buffer.from(mockBytecode).toString('base64'));
      expect(result.data!.dependencies).toEqual(mockDependencies);
      expect(result.data!.sourceCode).toBe(mockSourceCode);
      expect(result.data!.moduleName).toBe('test');
      expect(result.data!.metadata).toEqual(validMetadata);
      expect(result.data!.compilationId).toBeDefined();
    });

    it('should fail when metadata validation fails', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: false,
        errors: ['Invalid symbol format'],
      });

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_METADATA');
      expect(result.error?.message).toBe('Contract metadata validation failed');
      expect(result.error?.details).toEqual({ errors: ['Invalid symbol format'] });
    });

    it('should fail when compiler is not available', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(false);

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COMPILER_UNAVAILABLE');
      expect(result.error?.message).toBe('Move compiler is not available');
    });

    it('should fail when generated source code is invalid', async () => {
      const mockSourceCode = 'invalid move code';

      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(false);

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_SOURCE_CODE');
      expect(result.error?.message).toBe('Generated source code is invalid');
      expect(result.error?.details).toEqual({ sourceCode: mockSourceCode });
    });

    it('should handle compilation errors with appropriate error codes', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue('valid source');
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: [],
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockRejectedValue(new Error('timeout occurred'));

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COMPILATION_TIMEOUT');
      expect(result.error?.message).toContain('timeout occurred');
    });

    it('should handle syntax errors', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue('valid source');
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: [],
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockRejectedValue(new Error('syntax error in line 10'));

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('SYNTAX_ERROR');
      expect(result.error?.message).toContain('syntax error');
    });

    it('should handle dependency errors', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue('valid source');
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: [],
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockRejectedValue(new Error('dependency not found'));

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DEPENDENCY_ERROR');
      expect(result.error?.message).toContain('dependency not found');
    });

    it('should handle generic compilation failures', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue('valid source');
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: [],
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockRejectedValue(new Error('unknown error'));

      const result = await useCase.execute(validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COMPILATION_FAILED');
      expect(result.error?.message).toContain('unknown error');
    });

    it('should call all services in the correct order', async () => {
      const mockSourceCode = 'module coin_template::test { /* mock source */ }';
      const mockBytecode = new Uint8Array([1, 2, 3, 4]);

      // Setup mocks
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockCompilerService.isAvailable).mockResolvedValue(true);
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockCompilerService.validateSource).mockResolvedValue(true);
      vi.mocked(mockTemplateService.getTemplateConfig).mockResolvedValue({
        version: 'latest',
        templatePath: '/path/to/template',
        dependencies: [],
        supportedNetworks: ['TESTNET'],
      });
      vi.mocked(mockCompilerService.compile).mockResolvedValue({
        bytecode: mockBytecode,
        dependencies: [],
        sourceCode: mockSourceCode,
        moduleName: 'test',
        metadata: validMetadata,
        compilationId: 'test-compilation-id',
      });

      await useCase.execute(validRequest);

      // Verify call order
      expect(mockTemplateService.validateMetadata).toHaveBeenCalledWith(validMetadata);
      expect(mockCompilerService.isAvailable).toHaveBeenCalled();
      expect(mockTemplateService.generateSource).toHaveBeenCalledWith(validMetadata);
      expect(mockCompilerService.validateSource).toHaveBeenCalledWith(mockSourceCode);
      expect(mockTemplateService.getTemplateConfig).toHaveBeenCalledWith('latest');
      expect(mockCompilerService.compile).toHaveBeenCalledWith(mockSourceCode, []);
    });
  });
});
