import { describe, expect, it, beforeEach, vi } from 'vitest';

import type { ContractMetadata } from '../dto/contract-metadata.dto';
import type { IContractTemplateService } from '../interfaces/contract-compilation.interfaces';
import { GenerateContractUseCase } from './generate-contract.use-case';

// Mock template service
const mockTemplateService: IContractTemplateService = {
  generateSource: vi.fn(),
  getTemplateConfig: vi.fn(),
  validateMetadata: vi.fn(),
  generateModuleName: vi.fn(),
};

describe('GenerateContractUseCase', () => {
  let useCase: GenerateContractUseCase;

  beforeEach(() => {
    vi.clearAllMocks();
    useCase = new GenerateContractUseCase(mockTemplateService);
  });

  const validMetadata: ContractMetadata = {
    name: 'Test Token',
    symbol: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: 'https://twitter.com/test',
    website: 'https://example.com',
    telegram: 'https://t.me/test',
    totalSupply: 1000000,
    decimals: 6,
  };

  describe('execute', () => {
    it('should successfully generate source code for valid metadata', async () => {
      const mockSourceCode = `
        module coin_template::test {
          const NAME: vector<u8> = b"Test Token";
          const SYMBOL: vector<u8> = b"TEST";
          // ... rest of the source code
        }
      `;
      const mockModuleName = 'test_12345678';

      // Setup mocks
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockTemplateService.generateModuleName).mockReturnValue(mockModuleName);

      const result = await useCase.execute(validMetadata);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.sourceCode).toBe(mockSourceCode);
      expect(result.data!.moduleName).toBe(mockModuleName);
      expect(result.data!.metadata).toEqual(validMetadata);
      expect(result.error).toBeUndefined();
    });

    it('should fail when metadata validation fails', async () => {
      const validationErrors = ['Invalid symbol format', 'Total supply must be positive'];

      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: false,
        errors: validationErrors,
      });

      const result = await useCase.execute(validMetadata);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error!.code).toBe('INVALID_METADATA');
      expect(result.error!.message).toBe('Contract metadata validation failed');
      expect(result.error!.details).toEqual({ errors: validationErrors });
      expect(result.data).toBeUndefined();
    });

    it('should handle source generation errors', async () => {
      const errorMessage = 'Failed to generate source code';

      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockRejectedValue(new Error(errorMessage));

      const result = await useCase.execute(validMetadata);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error!.code).toBe('GENERATION_FAILED');
      expect(result.error!.message).toContain(errorMessage);
      expect(result.error!.details).toEqual({
        originalError: errorMessage,
        duration: expect.any(Number),
      });
      expect(result.data).toBeUndefined();
    });

    it('should call template service methods in correct order', async () => {
      const mockSourceCode = 'module coin_template::test { /* mock */ }';
      const mockModuleName = 'test_12345678';

      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockTemplateService.generateModuleName).mockReturnValue(mockModuleName);

      await useCase.execute(validMetadata);

      // Verify methods were called in the correct order
      expect(mockTemplateService.validateMetadata).toHaveBeenCalledWith(validMetadata);
      expect(mockTemplateService.generateSource).toHaveBeenCalledWith(validMetadata);
      expect(mockTemplateService.generateModuleName).toHaveBeenCalledWith(validMetadata.symbol);

      // Verify call order
      const calls = vi.mocked(mockTemplateService.validateMetadata).mock.invocationCallOrder;
      const generateCalls = vi.mocked(mockTemplateService.generateSource).mock.invocationCallOrder;
      const moduleNameCalls = vi.mocked(mockTemplateService.generateModuleName).mock.invocationCallOrder;

      expect(calls[0]).toBeLessThan(generateCalls[0]);
      expect(generateCalls[0]).toBeLessThan(moduleNameCalls[0]);
    });

    it('should handle different types of metadata', async () => {
      const minimalMetadata: ContractMetadata = {
        name: 'Minimal Token',
        symbol: 'MIN',
        totalSupply: 1000,
        decimals: 0,
      };

      const mockSourceCode = 'module coin_template::minimal { /* mock */ }';
      const mockModuleName = 'min_87654321';

      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockResolvedValue(mockSourceCode);
      vi.mocked(mockTemplateService.generateModuleName).mockReturnValue(mockModuleName);

      const result = await useCase.execute(minimalMetadata);

      expect(result.success).toBe(true);
      expect(result.data!.metadata).toEqual(minimalMetadata);
      expect(mockTemplateService.generateSource).toHaveBeenCalledWith(minimalMetadata);
    });

    it('should handle non-Error exceptions', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockRejectedValue('String error');

      const result = await useCase.execute(validMetadata);

      expect(result.success).toBe(false);
      expect(result.error!.message).toContain('String error');
      expect(result.error!.details).toEqual({
        originalError: 'String error',
        duration: expect.any(Number),
      });
    });

    it('should include timing information in error details', async () => {
      vi.mocked(mockTemplateService.validateMetadata).mockResolvedValue({
        valid: true,
        errors: [],
      });
      vi.mocked(mockTemplateService.generateSource).mockImplementation(async () => {
        // Add a small delay to ensure duration > 0
        await new Promise(resolve => setTimeout(resolve, 10));
        throw new Error('Test error');
      });

      const result = await useCase.execute(validMetadata);

      expect(result.success).toBe(false);
      expect(result.error!.details).toEqual({
        originalError: 'Test error',
        duration: expect.any(Number),
      });
      expect((result.error!.details as any).duration).toBeGreaterThan(0);
    });
  });
});
