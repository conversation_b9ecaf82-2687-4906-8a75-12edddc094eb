import { testClient } from 'hono/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { contractCompilation } from './contract-compilation.routes';
import type { ContractMetadata } from './dto/contract-metadata.dto';

// Mock the use cases
vi.mock('./use-cases/compile-contract.use-case');
vi.mock('./use-cases/generate-contract.use-case');

import { CompileContractUseCase } from './use-cases/compile-contract.use-case';
import { GenerateContractUseCase } from './use-cases/generate-contract.use-case';

const mockCompileUseCase = vi.mocked(CompileContractUseCase);
const mockGenerateUseCase = vi.mocked(GenerateContractUseCase);

describe('Contract Compilation Routes', () => {
  const client = testClient(contractCompilation);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const validMetadata: ContractMetadata = {
    name: 'Test Token',
    symbol: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: 'https://twitter.com/test',
    website: 'https://example.com',
    telegram: 'https://t.me/test',
    totalSupply: 1000000,
    decimals: 6,
  };

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await client.health.$get();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Contract compilation module is healthy');
      expect(data.data.version).toBe('1.0.0');
      expect(data.data.status).toBe('operational');
    });
  });

  describe('POST /generate', () => {
    it('should generate source code successfully', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: true,
        data: {
          sourceCode: 'module coin_template::test { /* mock */ }',
          moduleName: 'test_12345678',
          metadata: validMetadata,
        },
      });

      mockGenerateUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.generate.$post({
        json: validMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Contract source generated successfully');
      expect(data.data.sourceCode).toBeDefined();
      expect(data.data.moduleName).toBe('test_12345678');
      expect(mockExecute).toHaveBeenCalledWith(validMetadata);
    });

    it('should return 400 for invalid metadata', async () => {
      const invalidMetadata = {
        name: '', // Invalid empty name
        symbol: 'TEST',
        totalSupply: -1, // Invalid negative supply
        decimals: 6,
      };

      const response = await client.generate.$post({
        json: invalidMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('VALIDATION_ERROR');
      expect(data.error.message).toBe('Invalid contract metadata');
    });

    it('should handle generation failures', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'GENERATION_FAILED',
          message: 'Failed to generate source code',
          details: { originalError: 'Template error' },
        },
      });

      mockGenerateUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.generate.$post({
        json: validMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error.code).toBe('GENERATION_FAILED');
      expect(data.error.message).toBe('Failed to generate source code');
    });

    it('should handle invalid metadata validation errors', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'INVALID_METADATA',
          message: 'Contract metadata validation failed',
          details: { errors: ['Invalid symbol format'] },
        },
      });

      mockGenerateUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.generate.$post({
        json: validMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('INVALID_METADATA');
    });

    it('should handle internal server errors', async () => {
      const mockExecute = vi
        .fn()
        .mockRejectedValue(new Error('Unexpected error'));

      mockGenerateUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.generate.$post({
        json: validMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error.code).toBe('INTERNAL_ERROR');
      expect(data.error.message).toBe('Internal server error occurred');
    });
  });

  describe('POST /compile', () => {
    const validCompileRequest = {
      metadata: validMetadata,
      network: 'TESTNET' as const,
      templateVersion: 'latest',
    };

    it('should compile contract successfully', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: true,
        data: {
          bytecode: 'base64-encoded-bytecode',
          dependencies: ['0x1::string', '0x2::coin'],
          sourceCode: 'module coin_template::test { /* mock */ }',
          moduleName: 'test_12345678',
          metadata: validMetadata,
          compilationId: 'compilation-123',
        },
      });

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.compile.$post({
        json: validCompileRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Contract compiled successfully');
      expect(data.data.bytecode).toBe('base64-encoded-bytecode');
      expect(data.data.compilationId).toBe('compilation-123');
      expect(mockExecute).toHaveBeenCalledWith(validCompileRequest);
    });

    it('should return 400 for invalid compile request', async () => {
      const invalidRequest = {
        metadata: {
          name: '', // Invalid
          symbol: 'TEST',
          totalSupply: 1000000,
          decimals: 6,
        },
        network: 'INVALID_NETWORK', // Invalid network
      };

      const response = await client.compile.$post({
        json: invalidRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('VALIDATION_ERROR');
      expect(data.error.message).toBe('Invalid compilation request');
    });

    it('should handle compiler unavailable error', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'COMPILER_UNAVAILABLE',
          message: 'Move compiler is not available',
          details: { suggestion: 'Please ensure Sui CLI is installed' },
        },
      });

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.compile.$post({
        json: validCompileRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(503);
      expect(data.error.code).toBe('COMPILER_UNAVAILABLE');
    });

    it('should handle compilation timeout', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'COMPILATION_TIMEOUT',
          message: 'Compilation timed out',
          details: { duration: 60000 },
        },
      });

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.compile.$post({
        json: validCompileRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(408);
      expect(data.error.code).toBe('COMPILATION_TIMEOUT');
    });

    it('should handle syntax errors', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'SYNTAX_ERROR',
          message: 'Syntax error in generated code',
          details: { line: 10, column: 5 },
        },
      });

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.compile.$post({
        json: validCompileRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('SYNTAX_ERROR');
    });

    it('should handle generic compilation failures', async () => {
      const mockExecute = vi.fn().mockResolvedValue({
        success: false,
        error: {
          code: 'COMPILATION_FAILED',
          message: 'Compilation failed for unknown reason',
          details: { originalError: 'Unknown error' },
        },
      });

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            execute: mockExecute,
          }) as any,
      );

      const response = await client.compile.$post({
        json: validCompileRequest,
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error.code).toBe('COMPILATION_FAILED');
    });
  });

  describe('GET /compiler/status', () => {
    it('should return compiler status when available', async () => {
      const mockCompilerService = {
        isAvailable: vi.fn().mockResolvedValue(true),
        getCompilerVersion: vi.fn().mockResolvedValue('sui 1.0.0'),
      };

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            compilerService: mockCompilerService,
          }) as any,
      );

      const response = await client.compiler.status.$get();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Compiler status retrieved');
      expect(data.data.available).toBe(true);
      expect(data.data.version).toBe('sui 1.0.0');
      expect(data.data.status).toBe('ready');
    });

    it('should return compiler status when unavailable', async () => {
      const mockCompilerService = {
        isAvailable: vi.fn().mockResolvedValue(false),
        getCompilerVersion: vi
          .fn()
          .mockRejectedValue(new Error('Command not found')),
      };

      mockCompileUseCase.mockImplementation(
        () =>
          ({
            compilerService: mockCompilerService,
          }) as any,
      );

      const response = await client.compiler.status.$get();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.available).toBe(false);
      expect(data.data.version).toBe('unknown');
      expect(data.data.status).toBe('unavailable');
    });

    it('should handle errors when checking compiler status', async () => {
      mockCompileUseCase.mockImplementation(() => {
        throw new Error('Failed to initialize compiler service');
      });

      const response = await client.compiler.status.$get();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error.code).toBe('COMPILER_STATUS_ERROR');
      expect(data.error.message).toBe('Failed to get compiler status');
    });
  });
});
