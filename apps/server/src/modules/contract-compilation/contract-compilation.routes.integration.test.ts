import { testClient } from 'hono/testing';
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';

import { contractCompilation } from './contract-compilation.routes';
import type { ContractMetadata } from './dto/contract-metadata.dto';

// Mock the use cases to avoid real compilation
vi.mock('./use-cases/compile-contract.use-case');
vi.mock('./use-cases/generate-contract.use-case');

describe('Contract Compilation Integration Tests', () => {
  const client = testClient(contractCompilation);

  const validMetadata: ContractMetadata = {
    name: 'Integration Test Token',
    symbol: 'INTTEST',
    description: 'A token for integration testing',
    imageUrl: 'https://example.com/integration-test.png',
    twitter: 'https://twitter.com/inttest',
    website: 'https://inttest.example.com',
    telegram: 'https://t.me/inttest',
    totalSupply: 1000000,
    decimals: 6,
  };

  beforeAll(async () => {
    // Setup any required test environment
    // For example, ensure temp directories exist
  });

  afterAll(async () => {
    // Cleanup test environment
  });

  describe('Full Contract Generation and Compilation Flow', () => {
    it('should generate source code and then compile it successfully', async () => {
      // Step 1: Generate source code
      const generateResponse = await client.generate.$post({
        json: validMetadata,
      });
      const generateData = await generateResponse.json();

      expect(generateResponse.status).toBe(200);
      expect(generateData.success).toBe(true);
      expect(generateData.data.sourceCode).toBeDefined();
      expect(generateData.data.moduleName).toBeDefined();
      expect(generateData.data.sourceCode).toContain('module coin_template::');
      expect(generateData.data.sourceCode).toContain('Integration Test Token');
      expect(generateData.data.sourceCode).toContain('INTTEST');

      // Step 2: Compile the contract
      const compileRequest = {
        metadata: validMetadata,
        network: 'TESTNET' as const,
        templateVersion: 'latest',
      };

      const compileResponse = await client.compile.$post({
        json: compileRequest,
      });
      const compileData = await compileResponse.json();

      // Note: This might fail if Move compiler is not available in CI/test environment
      // In that case, we should mock the compiler service or skip this test
      if (compileResponse.status === 503) {
        console.warn(
          'Move compiler not available in test environment, skipping compilation test',
        );
        return;
      }

      expect(compileResponse.status).toBe(200);
      expect(compileData.success).toBe(true);
      expect(compileData.data.bytecode).toBeDefined();
      expect(compileData.data.dependencies).toBeInstanceOf(Array);
      expect(compileData.data.sourceCode).toBe(generateData.data.sourceCode);
      expect(compileData.data.compilationId).toBeDefined();
    });

    it('should handle the complete flow with minimal metadata', async () => {
      const minimalMetadata: ContractMetadata = {
        name: 'Minimal Token',
        symbol: 'MIN',
        totalSupply: 1000,
        decimals: 0,
      };

      // Generate source
      const generateResponse = await client.generate.$post({
        json: minimalMetadata,
      });
      const generateData = await generateResponse.json();

      expect(generateResponse.status).toBe(200);
      expect(generateData.data.sourceCode).toContain('Minimal Token');
      expect(generateData.data.sourceCode).toContain('MIN');
      expect(generateData.data.sourceCode).toContain(
        'const TOKEN_DECIMALS: u8 = 0',
      );
      expect(generateData.data.sourceCode).toContain(
        'const TOTAL_SUPPLY: u64 = 1000',
      );

      // Verify empty optional fields are handled correctly
      expect(generateData.data.sourceCode).toContain(
        'const DESCRIPTION: vector<u8> = b""',
      );
      expect(generateData.data.sourceCode).toContain(
        'const ICON_URL: vector<u8> = b""',
      );
      expect(generateData.data.sourceCode).toContain(
        'const TWITTER: vector<u8> = b""',
      );
      expect(generateData.data.sourceCode).toContain(
        'const WEBSITE: vector<u8> = b""',
      );
      expect(generateData.data.sourceCode).toContain(
        'const TELEGRAM: vector<u8> = b""',
      );
    });

    it('should validate metadata consistently across endpoints', async () => {
      const invalidMetadata = {
        name: '', // Invalid empty name
        symbol: 'INVALID-SYMBOL!', // Invalid characters
        totalSupply: -1, // Invalid negative supply
        decimals: 25, // Invalid decimals > 18
      };

      // Both endpoints should reject the same invalid metadata
      const generateResponse = await client.generate.$post({
        json: invalidMetadata,
      });
      expect(generateResponse.status).toBe(400);

      const compileResponse = await client.compile.$post({
        json: {
          metadata: invalidMetadata,
          network: 'TESTNET',
          templateVersion: 'latest',
        },
      });
      expect(compileResponse.status).toBe(400);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle malformed JSON requests', async () => {
      // Test with invalid JSON
      const response = await fetch(
        new URL('/generate', 'http://localhost').toString(),
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: '{ invalid json }',
        },
      );

      expect(response.status).toBe(400);
    });

    it('should handle missing required fields', async () => {
      const incompleteMetadata = {
        name: 'Test Token',
        // Missing required symbol and totalSupply
      };

      const response = await client.generate.$post({
        json: incompleteMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error.code).toBe('VALIDATION_ERROR');
      expect(data.error.details).toBeInstanceOf(Array);
    });

    it('should handle edge cases in metadata values', async () => {
      const edgeCaseMetadata: ContractMetadata = {
        name: 'A'.repeat(100), // Maximum length
        symbol: 'A'.repeat(20), // Maximum length
        description: 'B'.repeat(500), // Maximum length
        totalSupply: Number.MAX_SAFE_INTEGER,
        decimals: 18, // Maximum decimals
      };

      const response = await client.generate.$post({
        json: edgeCaseMetadata,
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.sourceCode).toContain('A'.repeat(100));
      expect(data.data.sourceCode).toContain('A'.repeat(20));
    });
  });

  describe('Performance and Concurrency', () => {
    it('should handle multiple concurrent generation requests', async () => {
      const requests = Array.from({ length: 5 }, (_, i) => ({
        name: `Concurrent Token ${i}`,
        symbol: `CONC${i}`,
        totalSupply: 1000000 + i,
        decimals: 6,
      }));

      const promises = requests.map((metadata) =>
        client.generate.$post({ json: metadata }),
      );

      const responses = await Promise.all(promises);

      // All requests should succeed
      responses.forEach((response, i) => {
        expect(response.status).toBe(200);
      });

      // Verify each response has unique content
      const responseData = await Promise.all(responses.map((r) => r.json()));

      const moduleNames = responseData.map((d) => d.data.moduleName);
      const uniqueModuleNames = new Set(moduleNames);
      expect(uniqueModuleNames.size).toBe(moduleNames.length);
    });

    it('should handle large metadata values efficiently', async () => {
      const largeMetadata: ContractMetadata = {
        name: 'Large Token Name '.repeat(5), // ~80 characters
        symbol: 'LARGE123456789012345', // 20 characters (max)
        description: 'This is a very long description. '.repeat(15), // ~480 characters
        imageUrl:
          'https://example.com/very-long-path-to-image-file-with-many-segments/image.png',
        twitter: 'https://twitter.com/very_long_twitter_handle_name',
        website: 'https://very-long-domain-name-for-testing.example.com',
        telegram: 'https://t.me/very_long_telegram_channel_name',
        totalSupply: 999999999999,
        decimals: 18,
      };

      const startTime = Date.now();
      const response = await client.generate.$post({
        json: largeMetadata,
      });
      const endTime = Date.now();

      expect(response.status).toBe(200);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds

      const data = await response.json();
      expect(data.data.sourceCode).toContain(largeMetadata.name);
      expect(data.data.sourceCode).toContain(largeMetadata.symbol);
      expect(data.data.sourceCode).toContain(largeMetadata.description);
    });
  });

  describe('Compiler Status Integration', () => {
    it('should provide accurate compiler status', async () => {
      const response = await client.compiler.status.$get();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.available).toBeDefined();
      expect(data.data.version).toBeDefined();
      expect(data.data.status).toMatch(/^(ready|unavailable)$/);

      if (data.data.available) {
        expect(data.data.status).toBe('ready');
        expect(data.data.version).not.toBe('unknown');
      } else {
        expect(data.data.status).toBe('unavailable');
      }
    });
  });
});
