import { logger } from '@hopfun/logger';
import { Hono } from 'hono';
import { StatusCodes } from 'http-status-codes';

import { formatResponse } from '@/utils/response';

import {
  CompileContractRequestSchema,
  ContractMetadataSchema,
} from './dto/contract-metadata.dto';
import { CompileContractUseCase } from './use-cases/compile-contract.use-case';
import { GenerateContractUseCase } from './use-cases/generate-contract.use-case';

const contractCompilation = new Hono()
  .get('/health', (c) => {
    return formatResponse(c, StatusCodes.OK, {
      message: 'Contract compilation module is healthy',
      data: {
        version: '1.0.0',
        status: 'operational',
      },
    });
  })

  // Generate Move source code from metadata
  .post('/generate', async (c) => {
    const startTime = Date.now();

    try {
      const body = await c.req.json();
      logger.info({ body }, 'Generate contract source request received');

      // Validate request body
      const validationResult = ContractMetadataSchema.safeParse(body);
      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            body,
          },
          'Invalid generate contract request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          message: 'Invalid contract metadata',
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      const metadata = validationResult.data;

      // Execute use case
      const generateUseCase = new GenerateContractUseCase();
      const result = await generateUseCase.execute(metadata);

      const duration = Date.now() - startTime;
      logger.info(
        {
          symbol: metadata.symbol,
          success: result.success,
          duration,
        },
        'Generate contract source request completed',
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Contract source generated successfully',
          data: result.data,
        });
      } else {
        const statusCode =
          result.error?.code === 'INVALID_METADATA'
            ? StatusCodes.BAD_REQUEST
            : StatusCodes.INTERNAL_SERVER_ERROR;

        return formatResponse(c, statusCode, {
          error: result.error,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          error: errorMessage,
          duration,
        },
        'Generate contract source request failed',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error occurred',
        error: {
          code: 'INTERNAL_ERROR',
          details: { originalError: errorMessage },
        },
      });
    }
  })

  // Compile Move contract to bytecode
  .post('/compile', async (c) => {
    const startTime = Date.now();

    try {
      const body = await c.req.json();
      logger.info({ body }, 'Compile contract request received');

      // Validate request body
      const validationResult = CompileContractRequestSchema.safeParse(body);
      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            body,
          },
          'Invalid compile contract request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          message: 'Invalid compilation request',
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      const request = validationResult.data;

      // Execute use case
      const compileUseCase = new CompileContractUseCase();
      const result = await compileUseCase.execute(request);

      const duration = Date.now() - startTime;
      logger.info(
        {
          symbol: request.metadata.symbol,
          network: request.network,
          success: result.success,
          duration,
        },
        'Compile contract request completed',
      );

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Contract compiled successfully',
          data: result.data,
        });
      } else {
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        // Map error codes to appropriate HTTP status codes
        switch (result.error?.code) {
          case 'INVALID_METADATA':
          case 'INVALID_SOURCE_CODE':
          case 'SYNTAX_ERROR':
            statusCode = StatusCodes.BAD_REQUEST;
            break;
          case 'COMPILER_UNAVAILABLE':
            statusCode = StatusCodes.SERVICE_UNAVAILABLE;
            break;
          case 'COMPILATION_TIMEOUT':
            statusCode = StatusCodes.REQUEST_TIMEOUT;
            break;
          default:
            statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
        }

        return formatResponse(c, statusCode, {
          error: result.error,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          error: errorMessage,
          duration,
        },
        'Compile contract request failed',
      );

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error occurred',
        error: {
          code: 'INTERNAL_ERROR',
          details: { originalError: errorMessage },
        },
      });
    }
  })

  // Get compiler status and version
  .get('/compiler/status', async (c) => {
    try {
      const compileUseCase = new CompileContractUseCase();

      // This is a bit of a hack to access the compiler service
      // In a real implementation, you might want to create a separate use case for this
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const compilerService = (compileUseCase as any).compilerService;

      const [isAvailable, version] = await Promise.allSettled([
        compilerService.isAvailable(),
        compilerService.getCompilerVersion(),
      ]);

      const available =
        isAvailable.status === 'fulfilled' ? isAvailable.value : false;
      const compilerVersion =
        version.status === 'fulfilled' ? version.value : 'unknown';

      return formatResponse(c, StatusCodes.OK, {
        message: 'Compiler status retrieved',
        data: {
          available,
          version: compilerVersion,
          status: available ? 'ready' : 'unavailable',
        },
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error({ error: errorMessage }, 'Failed to get compiler status');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Failed to get compiler status',
        error: {
          code: 'COMPILER_STATUS_ERROR',
          details: { originalError: errorMessage },
        },
      });
    }
  });

export { contractCompilation };
