import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Network, TokenStatus } from '@hopfun/database';

import { ListCoinsUseCase } from './list-coins.use-case';
import type { ITokenService } from '../../token/interfaces/token.interfaces';
import type { ListCoinsRequest } from '../dto/list-coins.dto';

// Mock token data
const mockToken = {
  id: 'token-id-1',
  curveId: 'curve-id-1',
  creator: '0x' + 'a'.repeat(64),
  coinName: 'Test Coin',
  ticker: 'TEST',
  description: 'A test coin for unit testing',
  imageUrl: 'https://example.com/image.png',
  twitter: 'https://twitter.com/test',
  website: 'https://test.com',
  telegram: 'https://t.me/test',
  totalSupply: '1000000000000000000',
  network: Network.TESTNET,
  status: TokenStatus.ACTIVE,
  currentPrice: '1000000',
  marketCap: '1000000000000000',
  virtualSuiAmount: '500000000000000',
  suiBalance: '250000000000000',
  tokenBalance: '750000000000000',
  availableTokenReserves: '750000000000000',
  poolId: null,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  completedAt: null,
  migratedAt: null,
  stats: {
    volume24h: '1000000000',
    volume7d: '7000000000',
    volumeTotal: '10000000000',
    transactions24h: 50,
    priceHigh24h: '1100000',
    priceLow24h: '900000',
    priceChange24h: '100000',
    priceChangePercent24h: '10.0',
    totalHolders: 25,
    lastTradeAt: new Date('2024-01-01T12:00:00Z'),
  },
};

describe('ListCoinsUseCase', () => {
  let listCoinsUseCase: ListCoinsUseCase;
  let mockTokenService: ITokenService;

  beforeEach(() => {
    mockTokenService = {
      getTokenById: vi.fn(),
      getTokenByCurveId: vi.fn(),
      getTokensByCreator: vi.fn(),
      listTokens: vi.fn(),
      verifyTokenExists: vi.fn(),
      verifyToken: vi.fn(),
    };

    listCoinsUseCase = new ListCoinsUseCase(mockTokenService);
    vi.clearAllMocks();
  });

  describe('list', () => {
    it('should return paginated coins successfully with default parameters', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(1);
      expect(result.data?.coins[0].id).toBe('token-id-1');
      expect(result.data?.coins[0].ticker).toBe('TEST');
      expect(result.data?.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockTokenService.listTokens).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        network: undefined,
        status: undefined,
        search: undefined,
        userAddress: undefined,
        createdAfter: undefined,
        createdBefore: undefined,
        minMarketCap: undefined,
        maxMarketCap: undefined,
      });
    });

    it('should handle lastTrade filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'lastTrade',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(
        expect.objectContaining({
          filter: 'lastTrade',
          sortBy: 'lastTrade',
          sortOrder: 'desc',
        }),
      );
    });

    it('should handle creationTime filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'creationTime',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(
        expect.objectContaining({
          filter: 'all',
          sortBy: 'createdAt',
          sortOrder: 'desc',
        }),
      );
    });

    it('should handle heatingUp filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'heatingUp',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(
        expect.objectContaining({
          filter: 'heatingUp',
        }),
      );
    });

    it('should handle watchlist filter with user address', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'watchlist',
        userAddress: '0x' + 'b'.repeat(64),
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(
        expect.objectContaining({
          filter: 'watchlist',
          userAddress: '0x' + 'b'.repeat(64),
        }),
      );
    });

    it('should return error when watchlist filter is used without user address', async () => {
      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'watchlist',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COIN_LISTING_ERROR');
      expect(result.error?.message).toBe('Failed to list coins');
    });

    it('should handle search functionality', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'all',
        search: 'test',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(
        expect.objectContaining({
          search: 'test',
        }),
      );
    });

    it('should handle pagination correctly', async () => {
      const mockTokens = Array(5).fill(mockToken);
      const mockResult = { tokens: mockTokens, total: 25 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 2,
        limit: 5,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(result.data?.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 25,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      });
    });

    it('should return empty result when no coins found', async () => {
      const mockResult = { tokens: [], total: 0 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(0);
      expect(result.data?.pagination.total).toBe(0);
    });

    it('should handle service errors gracefully', async () => {
      vi.mocked(mockTokenService.listTokens).mockRejectedValue(
        new Error('Database connection failed'),
      );

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COIN_LISTING_ERROR');
      expect(result.error?.message).toBe('Failed to list coins');
    });

    it('should convert token to coin DTO correctly', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: ListCoinsRequest = {
        page: 1,
        limit: 10,
        filter: 'all',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const result = await listCoinsUseCase.list(params);

      expect(result.success).toBe(true);
      const coin = result.data?.coins[0];
      expect(coin).toMatchObject({
        id: 'token-id-1',
        curveId: 'curve-id-1',
        coinName: 'Test Coin',
        ticker: 'TEST',
        contractAddress: 'curve-id-1',
        tradeFeePercent: 1.0,
        marketCapProgress: expect.any(Number),
        stats: {
          volume24h: '1000000000',
          transactions24h: 50,
          totalHolders: 25,
        },
      });
    });
  });
});
