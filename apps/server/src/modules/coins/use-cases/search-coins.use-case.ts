import type { Token } from '@hopfun/database';
import { logger } from '@hopfun/logger';

import type {
  ITokenService,
  ListTokensParams,
} from '../../token/interfaces/token.interfaces';
import { TokenService } from '../../token/services/token.service';
import type {
  CoinResponseDto,
  GetCoinResponseDto,
} from '../dto/list-coins.dto';
import type {
  GetCoinRequest,
  GetTrendingCoinsRequest,
  GetTrendingCoinsResponseDto,
  SearchCoinsRequest,
  SearchCoinsResponseDto,
} from '../dto/search-coins.dto';
import type { ISearchCoinsUseCase } from '../interfaces/coins.interfaces';
import {
  COIN_SEARCH_FIELDS,
  TRENDING_COINS_CONFIG,
} from '../interfaces/coins.interfaces';

export class SearchCoinsUseCase implements ISearchCoinsUseCase {
  private tokenService: ITokenService;

  constructor(tokenService?: ITokenService) {
    this.tokenService = tokenService ?? new TokenService();
  }

  async search(params: SearchCoinsRequest): Promise<SearchCoinsResponseDto> {
    const startTime = Date.now();

    logger.info(
      {
        query: params.q,
        page: params.page,
        limit: params.limit,
        sortBy: params.sortBy,
      },
      'Starting coin search',
    );

    try {
      // Map search parameters to token listing parameters
      const tokenParams: ListTokensParams = {
        page: params.page,
        limit: params.limit,
        network: params.network,
        status: params.status,
        search: params.q, // Use search query
        filter: 'all', // Search across all coins
        sortBy:
          params.sortBy === 'relevance' ? 'createdAt' : (params.sortBy as any),
        sortOrder: params.sortOrder,
      };

      // Get tokens using existing token service
      const result = await this.tokenService.listTokens(tokenParams);

      if (!result.tokens) {
        const duration = Date.now() - startTime;
        logger.info({ query: params.q, duration }, 'No coins found for search');

        return {
          success: true,
          data: {
            coins: [],
            pagination: {
              page: params.page,
              limit: params.limit,
              total: 0,
              totalPages: 0,
              hasNext: false,
              hasPrev: false,
            },
            searchQuery: params.q,
            searchFields: [...COIN_SEARCH_FIELDS],
          },
        };
      }

      // Convert tokens to coins
      const coins = result.tokens.map((token) => this.tokenToCoinDto(token));

      // Calculate pagination metadata
      const totalPages = Math.ceil(result.total / params.limit);
      const pagination = {
        page: params.page,
        limit: params.limit,
        total: result.total,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      };

      const duration = Date.now() - startTime;
      logger.info(
        {
          query: params.q,
          count: coins.length,
          total: result.total,
          duration,
        },
        'Coin search completed successfully',
      );

      return {
        success: true,
        data: {
          coins,
          pagination,
          searchQuery: params.q,
          searchFields: [...COIN_SEARCH_FIELDS],
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, query: params.q, duration },
        'Failed to search coins',
      );

      return {
        success: false,
        error: {
          code: 'COIN_SEARCH_ERROR',
          message: 'Failed to search coins',
          details: { originalError: errorMessage, query: params.q },
        },
      };
    }
  }

  async getTrending(
    params: GetTrendingCoinsRequest,
  ): Promise<GetTrendingCoinsResponseDto> {
    const startTime = Date.now();

    logger.info(
      {
        timeframe: params.timeframe,
        limit: params.limit,
        network: params.network,
      },
      'Starting trending coins retrieval',
    );

    try {
      // Map trending parameters to token listing parameters
      const tokenParams: ListTokensParams = {
        page: 1,
        limit: params.limit,
        network: params.network,
        status: 'ACTIVE', // Only active coins can be trending
        filter: 'heatingUp', // Use heating up filter for trending
        sortBy: 'volume24h', // Sort by volume for trending
        sortOrder: 'desc',
      };

      // Get tokens using existing token service
      const result = await this.tokenService.listTokens(tokenParams);

      if (!result.tokens) {
        const duration = Date.now() - startTime;
        logger.info(
          { timeframe: params.timeframe, duration },
          'No trending coins found',
        );

        return {
          success: true,
          data: {
            coins: [],
            timeframe: params.timeframe,
            generatedAt: new Date().toISOString(),
          },
        };
      }

      // Convert tokens to coins and filter by minimum activity
      const coins = result.tokens
        .filter((token) => {
          // Filter coins with minimum transaction activity
          const tokenWithStats = token as any; // Type assertion for stats
          return (
            tokenWithStats.stats &&
            tokenWithStats.stats.transactions24h >=
              TRENDING_COINS_CONFIG.minTransactions
          );
        })
        .map((token) => this.tokenToCoinDto(token))
        .slice(0, params.limit); // Ensure we don't exceed the requested limit

      const duration = Date.now() - startTime;
      logger.info(
        {
          timeframe: params.timeframe,
          count: coins.length,
          duration,
        },
        'Trending coins retrieved successfully',
      );

      return {
        success: true,
        data: {
          coins,
          timeframe: params.timeframe,
          generatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, timeframe: params.timeframe, duration },
        'Failed to get trending coins',
      );

      return {
        success: false,
        error: {
          code: 'TRENDING_COINS_ERROR',
          message: 'Failed to get trending coins',
          details: { originalError: errorMessage, timeframe: params.timeframe },
        },
      };
    }
  }

  async getById(params: GetCoinRequest): Promise<GetCoinResponseDto> {
    const startTime = Date.now();

    logger.info({ id: params.id }, 'Starting coin retrieval by ID');

    try {
      // Try to get token by ID first, then by curve ID
      let token = await this.tokenService.getTokenById(params.id);

      if (!token) {
        // Try by curve ID if not found by regular ID
        token = await this.tokenService.getTokenByCurveId(params.id);
      }

      if (!token) {
        const duration = Date.now() - startTime;
        logger.warn({ id: params.id, duration }, 'Coin not found');

        return {
          success: false,
          error: {
            code: 'COIN_NOT_FOUND',
            message: 'Coin not found',
            details: { id: params.id },
          },
        };
      }

      const coin = this.tokenToCoinDto(token);

      const duration = Date.now() - startTime;
      logger.info(
        { id: params.id, ticker: coin.ticker, duration },
        'Coin retrieved successfully',
      );

      return {
        success: true,
        data: coin,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, id: params.id, duration },
        'Failed to retrieve coin',
      );

      return {
        success: false,
        error: {
          code: 'COIN_RETRIEVAL_ERROR',
          message: 'Failed to retrieve coin',
          details: { originalError: errorMessage, id: params.id },
        },
      };
    }
  }

  /**
   * Convert Token to CoinResponseDto
   */
  private tokenToCoinDto(
    token: Token & { stats?: any; transactions?: any[] },
  ): CoinResponseDto {
    // Calculate market cap progress (percentage to graduation)
    const graduationThreshold = 70000000000000; // 70M SUI in wei
    const currentMarketCap = parseFloat(token.marketCap || '0');
    const marketCapProgress = Math.min(
      (currentMarketCap / graduationThreshold) * 100,
      100,
    );

    return {
      id: token.id,
      curveId: token.curveId,
      creator: token.creator,
      coinName: token.coinName,
      ticker: token.ticker,
      description: token.description,
      imageUrl: token.imageUrl || undefined,
      twitter: token.twitter,
      website: token.website,
      telegram: token.telegram,
      totalSupply: token.totalSupply,
      network: token.network,
      status: token.status,
      currentPrice: token.currentPrice,
      marketCap: token.marketCap,
      marketCapProgress,
      virtualSuiAmount: token.virtualSuiAmount,
      suiBalance: token.suiBalance,
      tokenBalance: token.tokenBalance,
      availableTokenReserves: token.availableTokenReserves,
      poolId: token.poolId || undefined,
      createdAt: token.createdAt.toISOString(),
      updatedAt: token.updatedAt.toISOString(),
      completedAt: token.completedAt?.toISOString(),
      migratedAt: token.migratedAt?.toISOString(),
      contractAddress: token.curveId, // Use curveId as contract address
      tradeFeePercent: 1.0, // Standard 1% trading fee
      graduationThreshold: graduationThreshold.toString(),
      stats: token.stats
        ? {
            volume24h: token.stats.volume24h || '0',
            volume7d: token.stats.volume7d || '0',
            volumeTotal: token.stats.volumeTotal || '0',
            transactions24h: token.stats.transactions24h || 0,
            priceHigh24h: token.stats.priceHigh24h || '0',
            priceLow24h: token.stats.priceLow24h || '0',
            priceChange24h: token.stats.priceChange24h || '0',
            priceChangePercent24h: token.stats.priceChangePercent24h || '0',
            totalHolders: token.stats.totalHolders || 0,
            lastTradeAt: token.stats.lastTradeAt?.toISOString(),
          }
        : undefined,
    };
  }
}
