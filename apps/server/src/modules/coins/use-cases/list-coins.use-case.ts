import type { Token } from '@hopfun/database';
import { logger } from '@hopfun/logger';

import type {
  ITokenService,
  ListTokensParams,
} from '../../token/interfaces/token.interfaces';
import { TokenService } from '../../token/services/token.service';
import type {
  CoinResponseDto,
  ListCoinsRequest,
  ListCoinsResponseDto,
} from '../dto/list-coins.dto';
import type { IListCoinsUseCase } from '../interfaces/coins.interfaces';
import { COIN_FILTER_MAPPINGS } from '../interfaces/coins.interfaces';

export class ListCoinsUseCase implements IListCoinsUseCase {
  private tokenService: ITokenService;

  constructor(tokenService?: ITokenService) {
    this.tokenService = tokenService ?? new TokenService();
  }

  async list(params: ListCoinsRequest): Promise<ListCoinsResponseDto> {
    const startTime = Date.now();

    logger.info(
      {
        params: {
          page: params.page,
          limit: params.limit,
          filter: params.filter,
          search: params.search,
          network: params.network,
          status: params.status,
        },
      },
      'Starting coin listing',
    );

    try {
      // Validate filter-specific requirements
      this.validateFilterRequirements(params);

      // Map coin filter to token filter
      const tokenParams = this.mapCoinParamsToTokenParams(params);

      // Get tokens using existing token service
      const result = await this.tokenService.listTokens(tokenParams);

      if (!result.tokens) {
        const duration = Date.now() - startTime;
        logger.warn({ duration }, 'No coins found');

        return {
          success: true,
          data: {
            coins: [],
            pagination: {
              page: params.page,
              limit: params.limit,
              total: 0,
              totalPages: 0,
              hasNext: false,
              hasPrev: false,
            },
          },
        };
      }

      // Convert tokens to coins
      const coins = result.tokens.map((token) => this.tokenToCoinDto(token));

      // Calculate pagination metadata
      const totalPages = Math.ceil(result.total / params.limit);
      const pagination = {
        page: params.page,
        limit: params.limit,
        total: result.total,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      };

      const duration = Date.now() - startTime;
      logger.info(
        {
          count: coins.length,
          total: result.total,
          page: params.page,
          limit: params.limit,
          filter: params.filter,
          duration,
        },
        'Coins listed successfully',
      );

      return {
        success: true,
        data: {
          coins,
          pagination,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, params, duration },
        'Failed to list coins',
      );

      return {
        success: false,
        error: {
          code: 'COIN_LISTING_ERROR',
          message: 'Failed to list coins',
          details: { originalError: errorMessage },
        },
      };
    }
  }

  /**
   * Validate filter-specific requirements
   */
  private validateFilterRequirements(params: ListCoinsRequest): void {
    // Watchlist filter requires user address
    if (params.filter === 'watchlist' && !params.userAddress) {
      throw new Error('User address is required for watchlist filter');
    }
  }

  /**
   * Map coin listing parameters to token listing parameters
   */
  private mapCoinParamsToTokenParams(
    params: ListCoinsRequest,
  ): ListTokensParams {
    const filterMapping = COIN_FILTER_MAPPINGS[params.filter];

    if (!filterMapping) {
      throw new Error(`Unknown filter: ${params.filter}`);
    }

    // Base token parameters
    const tokenParams: ListTokensParams = {
      page: params.page,
      limit: params.limit,
      network: params.network,
      status: params.status,
      search: params.search,
      filter: filterMapping.tokenFilter as any,
      sortBy: (filterMapping.sortBy as any) || params.sortBy,
      sortOrder: filterMapping.sortOrder || params.sortOrder,
      userAddress: params.userAddress,
      createdAfter: params.createdAfter,
      createdBefore: params.createdBefore,
      minMarketCap: params.minMarketCap,
      maxMarketCap: params.maxMarketCap,
    };

    return tokenParams;
  }

  /**
   * Convert Token to CoinResponseDto
   */
  private tokenToCoinDto(
    token: Token & { stats?: any; transactions?: any[] },
  ): CoinResponseDto {
    // Calculate market cap progress (percentage to graduation)
    const graduationThreshold = 70000000000000; // 70M SUI in wei
    const currentMarketCap = parseFloat(token.marketCap || '0');
    const marketCapProgress = Math.min(
      (currentMarketCap / graduationThreshold) * 100,
      100,
    );

    return {
      id: token.id,
      curveId: token.curveId,
      creator: token.creator,
      coinName: token.coinName,
      ticker: token.ticker,
      description: token.description,
      imageUrl: token.imageUrl || undefined,
      twitter: token.twitter,
      website: token.website,
      telegram: token.telegram,
      totalSupply: token.totalSupply,
      network: token.network,
      status: token.status,
      currentPrice: token.currentPrice,
      marketCap: token.marketCap,
      marketCapProgress,
      virtualSuiAmount: token.virtualSuiAmount,
      suiBalance: token.suiBalance,
      tokenBalance: token.tokenBalance,
      availableTokenReserves: token.availableTokenReserves,
      poolId: token.poolId || undefined,
      createdAt: token.createdAt.toISOString(),
      updatedAt: token.updatedAt.toISOString(),
      completedAt: token.completedAt?.toISOString(),
      migratedAt: token.migratedAt?.toISOString(),
      contractAddress: token.curveId, // Use curveId as contract address
      tradeFeePercent: 1.0, // Standard 1% trading fee
      graduationThreshold: graduationThreshold.toString(),
      stats: token.stats
        ? {
            volume24h: token.stats.volume24h || '0',
            volume7d: token.stats.volume7d || '0',
            volumeTotal: token.stats.volumeTotal || '0',
            transactions24h: token.stats.transactions24h || 0,
            priceHigh24h: token.stats.priceHigh24h || '0',
            priceLow24h: token.stats.priceLow24h || '0',
            priceChange24h: token.stats.priceChange24h || '0',
            priceChangePercent24h: token.stats.priceChangePercent24h || '0',
            totalHolders: token.stats.totalHolders || 0,
            lastTradeAt: token.stats.lastTradeAt?.toISOString(),
          }
        : undefined,
    };
  }
}
