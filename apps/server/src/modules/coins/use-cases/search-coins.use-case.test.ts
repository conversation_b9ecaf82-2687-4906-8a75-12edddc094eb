import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Network, TokenStatus } from '@hopfun/database';

import { SearchCoinsUseCase } from './search-coins.use-case';
import type { ITokenService } from '../../token/interfaces/token.interfaces';
import type {
  SearchCoinsRequest,
  GetTrendingCoinsRequest,
  GetCoinRequest,
} from '../dto/search-coins.dto';

// Mock token data
const mockToken = {
  id: 'token-id-1',
  curveId: 'curve-id-1',
  creator: '0x' + 'a'.repeat(64),
  coinName: 'Test Coin',
  ticker: 'TEST',
  description: 'A test coin for unit testing',
  imageUrl: 'https://example.com/image.png',
  twitter: 'https://twitter.com/test',
  website: 'https://test.com',
  telegram: 'https://t.me/test',
  totalSupply: '1000000000000000000',
  network: Network.TESTNET,
  status: TokenStatus.ACTIVE,
  currentPrice: '1000000',
  marketCap: '1000000000000000',
  virtualSuiAmount: '500000000000000',
  suiBalance: '250000000000000',
  tokenBalance: '750000000000000',
  availableTokenReserves: '750000000000000',
  poolId: null,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  completedAt: null,
  migratedAt: null,
  stats: {
    volume24h: '1000000000',
    volume7d: '7000000000',
    volumeTotal: '10000000000',
    transactions24h: 50,
    priceHigh24h: '1100000',
    priceLow24h: '900000',
    priceChange24h: '100000',
    priceChangePercent24h: '10.0',
    totalHolders: 25,
    lastTradeAt: new Date('2024-01-01T12:00:00Z'),
  },
};

describe('SearchCoinsUseCase', () => {
  let searchCoinsUseCase: SearchCoinsUseCase;
  let mockTokenService: ITokenService;

  beforeEach(() => {
    mockTokenService = {
      getTokenById: vi.fn(),
      getTokenByCurveId: vi.fn(),
      getTokensByCreator: vi.fn(),
      listTokens: vi.fn(),
      verifyTokenExists: vi.fn(),
      verifyToken: vi.fn(),
    };

    searchCoinsUseCase = new SearchCoinsUseCase(mockTokenService);
    vi.clearAllMocks();
  });

  describe('search', () => {
    it('should search coins successfully', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: SearchCoinsRequest = {
        q: 'test',
        page: 1,
        limit: 10,
        sortBy: 'relevance',
        sortOrder: 'desc',
      };

      const result = await searchCoinsUseCase.search(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(1);
      expect(result.data?.coins[0].ticker).toBe('TEST');
      expect(result.data?.searchQuery).toBe('test');
      expect(result.data?.searchFields).toEqual(['coinName', 'ticker', 'description']);
      expect(result.data?.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockTokenService.listTokens).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        search: 'test',
        filter: 'all',
        sortBy: 'createdAt', // relevance maps to createdAt
        sortOrder: 'desc',
        network: undefined,
        status: undefined,
      });
    });

    it('should handle empty search results', async () => {
      const mockResult = { tokens: [], total: 0 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: SearchCoinsRequest = {
        q: 'nonexistent',
        page: 1,
        limit: 10,
        sortBy: 'relevance',
        sortOrder: 'desc',
      };

      const result = await searchCoinsUseCase.search(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(0);
      expect(result.data?.searchQuery).toBe('nonexistent');
      expect(result.data?.pagination.total).toBe(0);
    });

    it('should handle search with filters', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: SearchCoinsRequest = {
        q: 'test',
        page: 1,
        limit: 10,
        network: 'TESTNET',
        status: 'ACTIVE',
        sortBy: 'marketCap',
        sortOrder: 'desc',
      };

      const result = await searchCoinsUseCase.search(params);

      expect(result.success).toBe(true);
      expect(mockTokenService.listTokens).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        search: 'test',
        filter: 'all',
        sortBy: 'marketCap',
        sortOrder: 'desc',
        network: 'TESTNET',
        status: 'ACTIVE',
      });
    });

    it('should handle search errors gracefully', async () => {
      vi.mocked(mockTokenService.listTokens).mockRejectedValue(
        new Error('Search failed'),
      );

      const params: SearchCoinsRequest = {
        q: 'test',
        page: 1,
        limit: 10,
        sortBy: 'relevance',
        sortOrder: 'desc',
      };

      const result = await searchCoinsUseCase.search(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COIN_SEARCH_ERROR');
      expect(result.error?.message).toBe('Failed to search coins');
    });
  });

  describe('getTrending', () => {
    it('should get trending coins successfully', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: GetTrendingCoinsRequest = {
        timeframe: '24h',
        limit: 10,
      };

      const result = await searchCoinsUseCase.getTrending(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(1);
      expect(result.data?.timeframe).toBe('24h');
      expect(result.data?.generatedAt).toBeDefined();
      expect(mockTokenService.listTokens).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        filter: 'heatingUp',
        sortBy: 'volume24h',
        sortOrder: 'desc',
        status: 'ACTIVE',
        network: undefined,
      });
    });

    it('should filter trending coins by minimum transactions', async () => {
      const lowActivityToken = { ...mockToken, stats: { ...mockToken.stats, transactions24h: 2 } };
      const highActivityToken = { ...mockToken, stats: { ...mockToken.stats, transactions24h: 10 } };
      const mockTokens = [lowActivityToken, highActivityToken];
      const mockResult = { tokens: mockTokens, total: 2 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: GetTrendingCoinsRequest = {
        timeframe: '24h',
        limit: 10,
      };

      const result = await searchCoinsUseCase.getTrending(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(1); // Only high activity token should be included
      expect(result.data?.coins[0].stats?.transactions24h).toBe(10);
    });

    it('should handle empty trending results', async () => {
      const mockResult = { tokens: [], total: 0 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params: GetTrendingCoinsRequest = {
        timeframe: '24h',
        limit: 10,
      };

      const result = await searchCoinsUseCase.getTrending(params);

      expect(result.success).toBe(true);
      expect(result.data?.coins).toHaveLength(0);
      expect(result.data?.timeframe).toBe('24h');
    });

    it('should handle trending errors gracefully', async () => {
      vi.mocked(mockTokenService.listTokens).mockRejectedValue(
        new Error('Trending failed'),
      );

      const params: GetTrendingCoinsRequest = {
        timeframe: '24h',
        limit: 10,
      };

      const result = await searchCoinsUseCase.getTrending(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TRENDING_COINS_ERROR');
      expect(result.error?.message).toBe('Failed to get trending coins');
    });
  });

  describe('getById', () => {
    it('should get coin by ID successfully', async () => {
      vi.mocked(mockTokenService.getTokenById).mockResolvedValue(mockToken);

      const params: GetCoinRequest = {
        id: 'token-id-1',
      };

      const result = await searchCoinsUseCase.getById(params);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('token-id-1');
      expect(result.data?.ticker).toBe('TEST');
      expect(mockTokenService.getTokenById).toHaveBeenCalledWith('token-id-1');
    });

    it('should get coin by curve ID when not found by regular ID', async () => {
      vi.mocked(mockTokenService.getTokenById).mockResolvedValue(null);
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(mockToken);

      const params: GetCoinRequest = {
        id: 'curve-id-1',
      };

      const result = await searchCoinsUseCase.getById(params);

      expect(result.success).toBe(true);
      expect(result.data?.curveId).toBe('curve-id-1');
      expect(mockTokenService.getTokenById).toHaveBeenCalledWith('curve-id-1');
      expect(mockTokenService.getTokenByCurveId).toHaveBeenCalledWith('curve-id-1');
    });

    it('should return error when coin not found', async () => {
      vi.mocked(mockTokenService.getTokenById).mockResolvedValue(null);
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(null);

      const params: GetCoinRequest = {
        id: 'nonexistent-id',
      };

      const result = await searchCoinsUseCase.getById(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COIN_NOT_FOUND');
      expect(result.error?.message).toBe('Coin not found');
    });

    it('should handle get by ID errors gracefully', async () => {
      vi.mocked(mockTokenService.getTokenById).mockRejectedValue(
        new Error('Database error'),
      );

      const params: GetCoinRequest = {
        id: 'token-id-1',
      };

      const result = await searchCoinsUseCase.getById(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('COIN_RETRIEVAL_ERROR');
      expect(result.error?.message).toBe('Failed to retrieve coin');
    });
  });
});
