import { z } from 'zod';

/**
 * Schema for listing coins with filtering and pagination options
 * Supports the 4 main filtering options specified in the PRD:
 * - Last trade (filter by most recent trading activity)
 * - Creation time (filter by when coins were created)
 * - Heating up (filter by trending/popular coins)
 * - Watchlist (filter by user's watchlisted coins)
 */
export const ListCoinsRequestSchema = z.object({
  // Pagination
  page: z.coerce.number().min(1, 'Page must be at least 1').default(1),
  limit: z.coerce
    .number()
    .min(1)
    .max(100, 'Limit must be between 1 and 100')
    .default(10), // Default 10 items per page as specified in PRD
  
  // Network filter
  network: z.enum(['MAINNET', 'TESTNET', 'DEVNET']).optional(),
  
  // Status filter
  status: z.enum(['ACTIVE', 'COMPLETED', 'MIGRATED']).optional(),
  
  // Main filtering options (4 options as specified in PRD)
  filter: z
    .enum([
      'all',        // No filter - show all coins
      'lastTrade',  // Filter by most recent trading activity
      'creationTime', // Filter by when coins were created (newest first)
      'heatingUp',  // Filter by trending/popular coins
      'watchlist',  // Filter by user's watchlisted coins
    ])
    .default('all'),
  
  // Search functionality (across name, symbol, description)
  search: z.string().optional(),
  
  // Sort options
  sortBy: z
    .enum([
      'createdAt',
      'marketCap',
      'currentPrice',
      'lastTrade',
      'volume24h',
      'priceChange24h'
    ])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  
  // User address for watchlist filter
  userAddress: z
    .string()
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format')
    .optional(),
  
  // Additional filters for advanced use cases
  createdAfter: z.string().datetime().optional(),
  createdBefore: z.string().datetime().optional(),
  minMarketCap: z.coerce.number().min(0).optional(),
  maxMarketCap: z.coerce.number().min(0).optional(),
});

export type ListCoinsRequest = z.infer<typeof ListCoinsRequestSchema>;

/**
 * Response DTO for coin data
 * Reuses the comprehensive token structure but with coin-specific terminology
 */
export interface CoinResponseDto {
  id: string;
  curveId: string;
  creator: string;
  coinName: string;
  ticker: string;
  description: string;
  imageUrl?: string;
  twitter: string;
  website: string;
  telegram: string;
  totalSupply: string;
  network: string;
  status: string;
  currentPrice: string;
  marketCap: string;
  marketCapProgress: number; // Percentage progress to graduation (0-100)
  virtualSuiAmount: string;
  suiBalance: string;
  tokenBalance: string;
  availableTokenReserves: string;
  poolId?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  migratedAt?: string;
  // Additional fields for comprehensive coin data
  contractAddress: string; // The coin contract address
  tradeFeePercent: number; // Trading fee percentage
  graduationThreshold: string; // Market cap threshold for graduation
  // Statistics
  stats?: {
    volume24h: string;
    volume7d: string;
    volumeTotal: string;
    transactions24h: number;
    priceHigh24h: string;
    priceLow24h: string;
    priceChange24h: string;
    priceChangePercent24h: string;
    totalHolders: number;
    lastTradeAt?: string;
  };
}

/**
 * Pagination metadata for coin listing responses
 */
export interface CoinPaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Response DTO for listing coins
 */
export interface ListCoinsResponseDto {
  success: boolean;
  data?: {
    coins: CoinResponseDto[];
    pagination: CoinPaginationMeta;
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

/**
 * Response DTO for single coin retrieval
 */
export interface GetCoinResponseDto {
  success: boolean;
  data?: CoinResponseDto;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
