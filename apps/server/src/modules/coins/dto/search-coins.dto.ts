import { z } from 'zod';
import type { CoinResponseDto, CoinPaginationMeta } from './list-coins.dto';

/**
 * Schema for searching coins across name, symbol, and description
 * Supports pagination and basic filtering
 */
export const SearchCoinsRequestSchema = z.object({
  // Search query (required)
  q: z.string().min(1, 'Search query is required').max(100, 'Search query too long'),
  
  // Pagination
  page: z.coerce.number().min(1, 'Page must be at least 1').default(1),
  limit: z.coerce
    .number()
    .min(1)
    .max(50, 'Limit must be between 1 and 50')
    .default(10), // Default 10 items per page
  
  // Optional filters
  network: z.enum(['MAINNET', 'TESTNET', 'DEVNET']).optional(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'MIGRATED']).optional(),
  
  // Sort options for search results
  sortBy: z
    .enum([
      'relevance',    // Default - sort by search relevance
      'createdAt',
      'marketCap',
      'volume24h',
      'priceChange24h'
    ])
    .default('relevance'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type SearchCoinsRequest = z.infer<typeof SearchCoinsRequestSchema>;

/**
 * Response DTO for coin search results
 */
export interface SearchCoinsResponseDto {
  success: boolean;
  data?: {
    coins: CoinResponseDto[];
    pagination: CoinPaginationMeta;
    searchQuery: string;
    searchFields: string[]; // Fields that were searched (name, symbol, description)
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

/**
 * Schema for getting trending coins
 */
export const GetTrendingCoinsRequestSchema = z.object({
  // Time frame for trending calculation
  timeframe: z.enum(['1h', '24h', '7d']).default('24h'),
  
  // Number of trending coins to return
  limit: z.coerce
    .number()
    .min(1)
    .max(50, 'Limit must be between 1 and 50')
    .default(10),
  
  // Network filter
  network: z.enum(['MAINNET', 'TESTNET', 'DEVNET']).optional(),
});

export type GetTrendingCoinsRequest = z.infer<typeof GetTrendingCoinsRequestSchema>;

/**
 * Response DTO for trending coins
 */
export interface GetTrendingCoinsResponseDto {
  success: boolean;
  data?: {
    coins: CoinResponseDto[];
    timeframe: string;
    generatedAt: string;
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

/**
 * Schema for getting a specific coin by ID or curve ID
 */
export const GetCoinRequestSchema = z.object({
  id: z.string().min(1, 'Coin ID is required'),
});

export type GetCoinRequest = z.infer<typeof GetCoinRequestSchema>;
