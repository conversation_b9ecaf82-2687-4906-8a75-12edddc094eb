import { Network, TokenStatus } from '@hopfun/database';
import { Hono } from 'hono';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { db } from '@/libs/database';
import { coins } from './coins.routes';

// Mock the database
vi.mock('@/libs/database', () => ({
  db: {
    token: {
      findMany: vi.fn(),
      count: vi.fn(),
      findUnique: vi.fn(),
    },
  },
}));

// Mock logger
vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

// Create test app
const app = new Hono().route('/api/coins', coins);

// Mock token data
const mockToken = {
  id: 'token-123',
  curveId: 'curve-123',
  creator: '0x' + 'a'.repeat(64),
  coinName: 'Test Coin',
  ticker: 'TEST',
  description: 'A test coin for integration testing',
  imageUrl: 'https://example.com/image.png',
  twitter: 'https://twitter.com/test',
  website: 'https://test.com',
  telegram: 'https://t.me/test',
  totalSupply: '**********000000000',
  network: Network.TESTNET,
  status: TokenStatus.ACTIVE,
  currentPrice: '1000000',
  marketCap: '**********000000',
  virtualSuiAmount: '500000000000000',
  suiBalance: '250000000000000',
  tokenBalance: '750000000000000',
  availableTokenReserves: '750000000000000',
  poolId: null,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  completedAt: null,
  migratedAt: null,
  stats: {
    volume24h: '**********',
    volume7d: '**********',
    volumeTotal: '**********0',
    transactions24h: 50,
    priceHigh24h: '1100000',
    priceLow24h: '900000',
    priceChange24h: '100000',
    priceChangePercent24h: '10.0',
    totalHolders: 25,
    lastTradeAt: new Date('2024-01-01T12:00:00Z'),
  },
  transactions: [],
};

describe('Coins API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /api/coins/health', () => {
    it('should return health status', async () => {
      const response = await app.request('/api/coins/health');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.message).toBe('Coins module is healthy');
      expect(json.data).toMatchObject({
        version: '1.0.0',
        status: 'operational',
      });
    });
  });

  describe('GET /api/coins', () => {
    it('should list coins with default parameters', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.message).toBe('Coins listed successfully');
      expect(json.data.coins).toHaveLength(1);
      expect(json.data.coins[0]).toMatchObject({
        id: 'token-123',
        curveId: 'curve-123',
        ticker: 'TEST',
        coinName: 'Test Coin',
        contractAddress: 'curve-123',
        tradeFeePercent: 1.0,
        marketCapProgress: expect.any(Number),
      });
      expect(json.data.pagination).toMatchObject({
        page: 1,
        limit: 10, // Default limit for coins
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it('should handle filter parameter', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins?filter=heatingUp');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.coins).toHaveLength(1);
    });

    it('should handle search parameter', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins?search=test');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.coins).toHaveLength(1);
    });

    it('should handle pagination parameters', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(25);

      const response = await app.request('/api/coins?page=2&limit=5');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.pagination).toMatchObject({
        page: 2,
        limit: 5,
        total: 25,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      });
    });

    it('should return validation error for invalid parameters', async () => {
      const response = await app.request('/api/coins?page=0&limit=200');
      const json = await response.json();

      expect(response.status).toBe(400);
      expect(json.status).toBe('error');
      expect(json.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle watchlist filter with user address', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const userAddress = '0x' + 'b'.repeat(64);
      const response = await app.request(
        `/api/coins?filter=watchlist&userAddress=${userAddress}`,
      );
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
    });
  });

  describe('GET /api/coins/search', () => {
    it('should search coins successfully', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins/search?q=test');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.message).toBe('Coin search completed successfully');
      expect(json.data.coins).toHaveLength(1);
      expect(json.data.searchQuery).toBe('test');
      expect(json.data.searchFields).toEqual([
        'coinName',
        'ticker',
        'description',
      ]);
      expect(json.data.pagination).toMatchObject({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      });
    });

    it('should return validation error for missing search query', async () => {
      const response = await app.request('/api/coins/search');
      const json = await response.json();

      expect(response.status).toBe(400);
      expect(json.status).toBe('error');
      expect(json.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle search with filters', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request(
        '/api/coins/search?q=test&network=TESTNET&status=ACTIVE',
      );
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.coins).toHaveLength(1);
    });

    it('should handle empty search results', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([]);
      vi.mocked(db.token.count).mockResolvedValue(0);

      const response = await app.request('/api/coins/search?q=nonexistent');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.coins).toHaveLength(0);
      expect(json.data.searchQuery).toBe('nonexistent');
    });
  });

  describe('GET /api/coins/trending', () => {
    it('should get trending coins successfully', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins/trending');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.message).toBe('Trending coins retrieved successfully');
      expect(json.data.coins).toHaveLength(1);
      expect(json.data.timeframe).toBe('24h'); // Default timeframe
      expect(json.data.generatedAt).toBeDefined();
    });

    it('should handle timeframe parameter', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([mockToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request(
        '/api/coins/trending?timeframe=7d&limit=5',
      );
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.timeframe).toBe('7d');
    });

    it('should filter out low activity coins', async () => {
      const lowActivityToken = {
        ...mockToken,
        stats: { ...mockToken.stats, transactions24h: 2 },
      };
      vi.mocked(db.token.findMany).mockResolvedValue([lowActivityToken] as any);
      vi.mocked(db.token.count).mockResolvedValue(1);

      const response = await app.request('/api/coins/trending');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.data.coins).toHaveLength(0); // Should be filtered out due to low activity
    });
  });

  describe('GET /api/coins/:id', () => {
    it('should get coin by ID successfully', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(mockToken as any);

      const response = await app.request('/api/coins/token-123');
      const json = await response.json();

      expect(response.status).toBe(200);
      expect(json.status).toBe('success');
      expect(json.message).toBe('Coin retrieved successfully');
      expect(json.data).toMatchObject({
        id: 'token-123',
        curveId: 'curve-123',
        ticker: 'TEST',
        contractAddress: 'curve-123',
        tradeFeePercent: 1.0,
      });
    });

    it('should return 404 when coin not found', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(null);

      const response = await app.request('/api/coins/nonexistent-id');
      const json = await response.json();

      expect(response.status).toBe(404);
      expect(json.status).toBe('error');
      expect(json.error.code).toBe('COIN_NOT_FOUND');
    });

    it('should return 404 for empty ID with trailing slash', async () => {
      const response = await app.request('/api/coins/');

      // Trailing slash creates a different route that doesn't exist
      expect(response.status).toBe(404);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      vi.mocked(db.token.findMany).mockRejectedValue(
        new Error('Database connection failed'),
      );

      const response = await app.request('/api/coins');
      const json = await response.json();

      expect(response.status).toBe(500);
      expect(json.status).toBe('error');
      expect(json.message).toBe('Failed to list coins');
      expect(json.error.code).toBe('COIN_LISTING_ERROR');
    });

    it('should handle search database errors gracefully', async () => {
      vi.mocked(db.token.findMany).mockRejectedValue(
        new Error('Search failed'),
      );

      const response = await app.request('/api/coins/search?q=test');
      const json = await response.json();

      expect(response.status).toBe(500);
      expect(json.status).toBe('error');
      expect(json.message).toBe('Failed to search coins');
    });

    it('should handle trending database errors gracefully', async () => {
      vi.mocked(db.token.findMany).mockRejectedValue(
        new Error('Trending failed'),
      );

      const response = await app.request('/api/coins/trending');
      const json = await response.json();

      expect(response.status).toBe(500);
      expect(json.status).toBe('error');
      expect(json.message).toBe('Failed to get trending coins');
    });
  });
});
