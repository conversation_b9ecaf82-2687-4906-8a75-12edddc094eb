import type {
  ListCoinsRequest,
  ListCoinsResponseDto,
  GetCoinResponseDto,
  CoinResponseDto,
} from '../dto/list-coins.dto';
import type {
  SearchCoinsRequest,
  SearchCoinsResponseDto,
  GetTrendingCoinsRequest,
  GetTrendingCoinsResponseDto,
  GetCoinRequest,
} from '../dto/search-coins.dto';

/**
 * Interface for coin listing use case
 */
export interface IListCoinsUseCase {
  /**
   * List coins with filtering, searching, and pagination
   */
  list(params: ListCoinsRequest): Promise<ListCoinsResponseDto>;
}

/**
 * Interface for coin search use case
 */
export interface ISearchCoinsUseCase {
  /**
   * Search coins by name, symbol, or description
   */
  search(params: SearchCoinsRequest): Promise<SearchCoinsResponseDto>;
  
  /**
   * Get trending coins based on recent activity
   */
  getTrending(params: GetTrendingCoinsRequest): Promise<GetTrendingCoinsResponseDto>;
  
  /**
   * Get a specific coin by ID
   */
  getById(params: GetCoinRequest): Promise<GetCoinResponseDto>;
}

/**
 * Error response interface for coins module
 */
export interface CoinErrorResponse {
  code: string;
  message: string;
  details?: unknown;
}

/**
 * Configuration interface for coins module
 */
export interface CoinsConfig {
  database: {
    connectionString: string;
  };
  pagination: {
    defaultLimit: number;
    maxLimit: number;
  };
  search: {
    maxQueryLength: number;
    searchFields: string[];
  };
}

/**
 * Filter mapping interface for converting coin filters to token filters
 */
export interface FilterMapping {
  coinFilter: string;
  tokenFilter: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  additionalParams?: Record<string, unknown>;
}

/**
 * Utility type to convert Token to Coin DTO
 */
export type TokenToCoinDto = (token: any) => CoinResponseDto;

/**
 * Constants for coin filtering and search
 */
export const COIN_FILTER_MAPPINGS: Record<string, FilterMapping> = {
  all: {
    coinFilter: 'all',
    tokenFilter: 'all',
  },
  lastTrade: {
    coinFilter: 'lastTrade',
    tokenFilter: 'lastTrade',
    sortBy: 'lastTrade',
    sortOrder: 'desc',
  },
  creationTime: {
    coinFilter: 'creationTime',
    tokenFilter: 'all', // Use 'all' filter but sort by createdAt
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  heatingUp: {
    coinFilter: 'heatingUp',
    tokenFilter: 'heatingUp',
  },
  watchlist: {
    coinFilter: 'watchlist',
    tokenFilter: 'watchlist',
    additionalParams: {
      requiresUserAddress: true,
    },
  },
};

/**
 * Search field mappings for coin search functionality
 */
export const COIN_SEARCH_FIELDS = ['coinName', 'ticker', 'description'] as const;

/**
 * Default pagination settings for coins
 */
export const COIN_PAGINATION_DEFAULTS = {
  defaultLimit: 10,
  maxLimit: 100,
  maxSearchLimit: 50,
} as const;

/**
 * Trending coins calculation settings
 */
export const TRENDING_COINS_CONFIG = {
  timeframes: {
    '1h': 1,
    '24h': 24,
    '7d': 168,
  },
  minTransactions: 5, // Minimum transactions to be considered trending
  maxResults: 50,
} as const;
