import { logger } from '@hopfun/logger';
import { Hono } from 'hono';
import { StatusCodes } from 'http-status-codes';

import { formatResponse } from '@/utils/response';

import { ListCoinsRequestSchema } from './dto/list-coins.dto';
import {
  SearchCoinsRequestSchema,
  GetTrendingCoinsRequestSchema,
  GetCoinRequestSchema,
} from './dto/search-coins.dto';
import { ListCoinsUseCase } from './use-cases/list-coins.use-case';
import { SearchCoinsUseCase } from './use-cases/search-coins.use-case';

const coins = new Hono()
  .get('/health', (c) => {
    return formatResponse(c, StatusCodes.OK, {
      message: 'Coins module is healthy',
      data: {
        version: '1.0.0',
        status: 'operational',
      },
    });
  })
  .get('/', async (c) => {
    try {
      // Get query parameters
      const page = c.req.query('page');
      const limit = c.req.query('limit');
      const network = c.req.query('network');
      const status = c.req.query('status');
      const filter = c.req.query('filter');
      const search = c.req.query('search');
      const sortBy = c.req.query('sortBy');
      const sortOrder = c.req.query('sortOrder');
      const userAddress = c.req.query('userAddress');
      const createdAfter = c.req.query('createdAfter');
      const createdBefore = c.req.query('createdBefore');
      const minMarketCap = c.req.query('minMarketCap');
      const maxMarketCap = c.req.query('maxMarketCap');

      // Validate request
      const validationResult = ListCoinsRequestSchema.safeParse({
        page,
        limit,
        network,
        status,
        filter,
        search,
        sortBy,
        sortOrder,
        userAddress,
        createdAfter,
        createdBefore,
        minMarketCap,
        maxMarketCap,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            query: {
              page, limit, network, status, filter, search,
              sortBy, sortOrder, userAddress, createdAfter,
              createdBefore, minMarketCap, maxMarketCap,
            },
          },
          'Invalid list coins request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const listCoinsUseCase = new ListCoinsUseCase();
      const result = await listCoinsUseCase.list(validationResult.data);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Coins listed successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in list coins');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/search', async (c) => {
    try {
      // Get query parameters
      const q = c.req.query('q');
      const page = c.req.query('page');
      const limit = c.req.query('limit');
      const network = c.req.query('network');
      const status = c.req.query('status');
      const sortBy = c.req.query('sortBy');
      const sortOrder = c.req.query('sortOrder');

      // Validate request
      const validationResult = SearchCoinsRequestSchema.safeParse({
        q,
        page,
        limit,
        network,
        status,
        sortBy,
        sortOrder,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            query: { q, page, limit, network, status, sortBy, sortOrder },
          },
          'Invalid search coins request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const searchCoinsUseCase = new SearchCoinsUseCase();
      const result = await searchCoinsUseCase.search(validationResult.data);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Coin search completed successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in search coins');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/trending', async (c) => {
    try {
      // Get query parameters
      const timeframe = c.req.query('timeframe');
      const limit = c.req.query('limit');
      const network = c.req.query('network');

      // Validate request
      const validationResult = GetTrendingCoinsRequestSchema.safeParse({
        timeframe,
        limit,
        network,
      });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            query: { timeframe, limit, network },
          },
          'Invalid get trending coins request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const searchCoinsUseCase = new SearchCoinsUseCase();
      const result = await searchCoinsUseCase.getTrending(validationResult.data);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Trending coins retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in get trending coins');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  })
  .get('/:id', async (c) => {
    try {
      // Get ID from path parameters
      const id = c.req.param('id');

      // Validate request
      const validationResult = GetCoinRequestSchema.safeParse({ id });

      if (!validationResult.success) {
        logger.warn(
          {
            errors: validationResult.error.issues,
            id,
          },
          'Invalid get coin by ID request',
        );

        return formatResponse(c, StatusCodes.BAD_REQUEST, {
          error: {
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        });
      }

      // Execute use case
      const searchCoinsUseCase = new SearchCoinsUseCase();
      const result = await searchCoinsUseCase.getById(validationResult.data);

      if (result.success) {
        return formatResponse(c, StatusCodes.OK, {
          message: 'Coin retrieved successfully',
          data: result.data,
        });
      } else {
        // Determine status code based on error code
        let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

        if (result.error?.code === 'COIN_NOT_FOUND') {
          statusCode = StatusCodes.NOT_FOUND;
        } else if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = StatusCodes.BAD_REQUEST;
        } else if (result.error?.code === 'DATABASE_ERROR') {
          statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        }

        return formatResponse(c, statusCode, {
          message: result.error?.message,
          error: {
            code: result.error?.code ?? 'UNKNOWN_ERROR',
            details: result.error?.details,
          },
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage }, 'Unexpected error in get coin by ID');

      return formatResponse(c, StatusCodes.INTERNAL_SERVER_ERROR, {
        message: 'Internal server error',
        error: {
          code: 'INTERNAL_ERROR',
        },
      });
    }
  });

export { coins };
