// eslint-disable-next-line simple-import-sort/imports
import { Hono } from 'hono';
import { rateLimiter } from 'hono-rate-limiter';
import { cors } from 'hono/cors';
import { csrf } from 'hono/csrf';
import { etag } from 'hono/etag';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { timing } from 'hono/timing';

import { generateUlid } from '@/libs/ulid';
import { errorHandlers } from '@/middlewares/error-handler';
import { requestLogger } from '@/middlewares/request-logger';
import { coins } from '@/modules/coins/coins.routes';
import { contractCompilation } from '@/modules/contract-compilation/contract-compilation.routes';
import { healthCheck } from '@/modules/health-check/health-check.routes';
import { token } from '@/modules/token/token.routes';
import { wallet } from '@/modules/wallet/wallet.routes';
import { formatResponse } from '@/utils/response';
import { StatusCodes } from 'http-status-codes';

const app = new Hono();

app.use('*', cors());
app.use('*', secureHeaders());
app.use('*', etag());
app.use('*', prettyJSON());
app.use('*', csrf());
app.use(timing());
app.use('*', requestLogger());

app.use(
  rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    limit: 100, // Limit each IP to 100 requests per `window` (here, per 15 minutes).
    standardHeaders: 'draft-6',
    keyGenerator: () => generateUlid(),
  }),
);

app.notFound((c) =>
  formatResponse(c, StatusCodes.NOT_FOUND, { message: 'Not found' }),
);

app.onError((error, c) => errorHandlers(error, c));

const _routes = app
  .route('/api/health', healthCheck)
  .route('/api/token', token)
  .route('/api/coins', coins)
  .route('/api/wallet', wallet)
  .route('/api/contract-compilation', contractCompilation);

export type AppType = typeof _routes;
export { app };
