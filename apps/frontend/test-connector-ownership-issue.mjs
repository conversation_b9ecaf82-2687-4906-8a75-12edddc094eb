#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to investigate the Connector ownership issue
 * This helps understand why the accept_connector transaction is failing with EUnableToReceiveObject
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    hopfunPackageId: '0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c',
    memeConfigId: '0xe4341287bd48105e879dfba7bc3151b5bd881efb23c6b5dbe0efefa896b0d7aa',
    hopdexPackageId: '0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac',
    hopdexConfigId: '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
    registryId: '0x51fdbf98bb1b5e9a574ce2c380cd0d59301db9e75484a17387d15bb5ef5d3d7d',
    registryPackageId: '0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e',
  },
};

async function investigateConnectorOwnership() {
  console.log('🔍 Investigating Connector Ownership Issue\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  // Step 1: Check MemeConfig object details
  console.log('📋 Step 1: Checking MemeConfig object...');
  try {
    const memeConfigObject = await client.getObject({
      id: config.memeConfigId,
      options: {
        showType: true,
        showContent: true,
        showOwner: true
      }
    });
    
    if (memeConfigObject.data) {
      console.log('✅ MemeConfig found:');
      console.log(`   Object ID: ${memeConfigObject.data.objectId}`);
      console.log(`   Type: ${memeConfigObject.data.type}`);
      console.log(`   Owner: ${JSON.stringify(memeConfigObject.data.owner)}`);
      console.log(`   Version: ${memeConfigObject.data.version}`);
      
      // Check if it's a shared object
      if (memeConfigObject.data.owner === 'Shared') {
        console.log('🔍 MemeConfig is a SHARED object (this is the issue!)');
      } else {
        console.log('🔍 MemeConfig owner type:', memeConfigObject.data.owner);
      }
    } else {
      console.log('❌ MemeConfig not found');
    }
  } catch (error) {
    console.log('❌ Error checking MemeConfig:', error.message);
  }

  // Step 2: Look for Connector objects owned by MemeConfig address
  console.log('\n📋 Step 2: Looking for Connector objects...');
  try {
    // Get the MemeConfig address (this is what Connectors are transferred to)
    const memeConfigAddress = config.memeConfigId;
    
    console.log(`🔍 Searching for objects owned by MemeConfig address: ${memeConfigAddress}`);
    
    const ownedObjects = await client.getOwnedObjects({
      owner: memeConfigAddress,
      options: {
        showType: true,
        showContent: true
      }
    });
    
    console.log(`📦 Found ${ownedObjects.data.length} objects owned by MemeConfig address`);
    
    const connectorObjects = ownedObjects.data.filter(obj => 
      obj.data?.type?.includes('::connector::Connector')
    );
    
    console.log(`🔗 Found ${connectorObjects.length} Connector objects`);
    
    if (connectorObjects.length > 0) {
      console.log('✅ Connector objects found:');
      connectorObjects.forEach((obj, index) => {
        console.log(`   ${index + 1}. Object ID: ${obj.data.objectId}`);
        console.log(`      Type: ${obj.data.type}`);
        console.log(`      Version: ${obj.data.version}`);
      });
    } else {
      console.log('❌ No Connector objects found owned by MemeConfig address');
    }
    
  } catch (error) {
    console.log('❌ Error searching for Connector objects:', error.message);
  }

  // Step 3: Explain the issue
  console.log('\n🔍 Analysis:');
  console.log('The issue is a fundamental mismatch in object ownership patterns:');
  console.log('');
  console.log('1. 📤 create_connector: Transfers Connector to MemeConfig ADDRESS');
  console.log('   - Uses: transfer::public_transfer(connector, meme_config_address)');
  console.log('   - Result: Connector is owned by the MemeConfig address');
  console.log('');
  console.log('2. 📥 accept_connector: Tries to receive Connector from MemeConfig OBJECT');
  console.log('   - Uses: transfer::public_receive(config.id(), sent)');
  console.log('   - Problem: MemeConfig is SHARED, not owned, so it can\'t own other objects');
  console.log('');
  console.log('🚨 ROOT CAUSE:');
  console.log('   - MemeConfig is a shared object (owner: "Shared")');
  console.log('   - Shared objects cannot own other objects');
  console.log('   - The Connector is transferred to MemeConfig\'s address, not to the object');
  console.log('   - accept_connector tries to receive from the shared object, which fails');
  console.log('');
  console.log('💡 SOLUTION:');
  console.log('   The accept_connector function should take the Connector as a direct parameter');
  console.log('   instead of using the Receiving<Connector<T>> pattern, since the MemeConfig');
  console.log('   is shared and cannot own the Connector object.');
}

async function main() {
  try {
    await investigateConnectorOwnership();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
