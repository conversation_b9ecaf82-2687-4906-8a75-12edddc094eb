#!/usr/bin/env node

/**
 * Test script to verify the temp_id collision fix
 * This tests the new dynamic temp_id generation and retry logic
 */

// Mock the TokenCreationService to test temp_id generation
class MockTokenCreationService {
  /**
   * Generate a unique temp_id with high entropy to avoid collisions
   * Uses timestamp, random number, and user address for uniqueness
   */
  static generateUniqueTempId(userAddress) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);

    // Use user address for additional entropy if available
    let userSuffix = 0;
    if (userAddress) {
      try {
        // Take last 8 characters of address and convert to number
        const addressSuffix = userAddress.slice(-8);
        userSuffix = parseInt(addressSuffix, 16) % 1000000;
      } catch (error) {
        console.warn('Failed to parse user address for temp_id:', error);
        userSuffix = Math.floor(Math.random() * 1000000);
      }
    } else {
      userSuffix = Math.floor(Math.random() * 1000000);
    }

    // Combine timestamp, random, and user suffix
    // Ensure the result fits in u64 (< 2^64)
    const tempId =
      ((timestamp % 1000000000) * 1000000 + random + userSuffix) %
      Number.MAX_SAFE_INTEGER;

    console.log('🎲 Generated unique temp_id:', tempId);
    return tempId;
  }

  /**
   * Check if an error is due to temp_id collision (EUniqueIdRequired)
   */
  static isTempIdCollisionError(error) {
    const errorMessage = error?.message || error?.toString() || '';
    // Check for MoveAbort with error code 3 (EUniqueIdRequired)
    // Also check for "Dry run failed" which can contain the MoveAbort error
    return (
      (errorMessage.includes('MoveAbort') && errorMessage.includes(', 3)')) ||
      (errorMessage.includes('Dry run failed') &&
        errorMessage.includes('MoveAbort') &&
        errorMessage.includes('place_dev_order') &&
        errorMessage.includes(', 3)'))
    );
  }

  /**
   * Execute place_dev_order with retry logic for temp_id collisions
   */
  static async executeWithTempIdRetry(
    signAndExecuteTransaction,
    createTransactionFn,
    maxRetries = 3,
    userAddress,
  ) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const tempId = this.generateUniqueTempId(userAddress);
      console.log(`🎲 Attempt ${attempt}/${maxRetries} with temp_id:`, tempId);

      try {
        const transaction = createTransactionFn(tempId);
        const result = await signAndExecuteTransaction({ transaction });
        console.log(`✅ Success on attempt ${attempt} with temp_id:`, tempId);
        return { result, tempId };
      } catch (error) {
        console.log(`❌ Attempt ${attempt} failed:`, error.message);
        lastError = error;

        if (this.isTempIdCollisionError(error)) {
          console.log(
            `🔄 Temp_id collision detected, retrying with new temp_id...`,
          );
          // Add small delay before retry
          await new Promise((resolve) => setTimeout(resolve, 100 * attempt));
          continue;
        } else {
          // Non-collision error, don't retry
          console.log('❌ Non-collision error, not retrying');
          throw error;
        }
      }
    }

    console.log(`❌ All ${maxRetries} attempts failed`);
    throw lastError;
  }
}

async function testTempIdGeneration() {
  console.log('🧪 Testing Temp ID Generation\n');

  // Test 1: Generate multiple temp_ids and ensure they're unique
  console.log('📋 Test 1: Uniqueness of generated temp_ids');
  const userAddress = '0x1234567890abcdef1234567890abcdef12345678';
  const tempIds = new Set();
  const numTests = 100;

  for (let i = 0; i < numTests; i++) {
    const tempId = MockTokenCreationService.generateUniqueTempId(userAddress);
    if (tempIds.has(tempId)) {
      console.log(`❌ Collision detected! temp_id ${tempId} already exists`);
      return false;
    }
    tempIds.add(tempId);
  }

  console.log(`✅ Generated ${numTests} unique temp_ids successfully\n`);

  // Test 2: Test different user addresses produce different temp_ids
  console.log('📋 Test 2: Different users generate different temp_ids');
  const user1 = '0x1111111111111111111111111111111111111111';
  const user2 = '0x2222222222222222222222222222222222222222';
  
  const user1TempIds = [];
  const user2TempIds = [];
  
  for (let i = 0; i < 10; i++) {
    user1TempIds.push(MockTokenCreationService.generateUniqueTempId(user1));
    user2TempIds.push(MockTokenCreationService.generateUniqueTempId(user2));
  }

  // Check if there's any overlap (there shouldn't be)
  const overlap = user1TempIds.filter(id => user2TempIds.includes(id));
  if (overlap.length > 0) {
    console.log(`❌ Found overlap between users: ${overlap}`);
    return false;
  }

  console.log('✅ Different users generate different temp_ids\n');

  // Test 3: Test error detection
  console.log('📋 Test 3: Error detection for temp_id collisions');
  
  const collisionError1 = new Error('MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)');
  const collisionError2 = new Error('Dry run failed, could not automatically determine a budget: MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)');
  const nonCollisionError = new Error('User rejection | (UserRejectionError:-4005)');

  const isCollision1 = MockTokenCreationService.isTempIdCollisionError(collisionError1);
  const isCollision2 = MockTokenCreationService.isTempIdCollisionError(collisionError2);
  const isNotCollision = MockTokenCreationService.isTempIdCollisionError(nonCollisionError);

  if (!isCollision1) {
    console.log('❌ Failed to detect MoveAbort collision error');
    return false;
  }

  if (!isCollision2) {
    console.log('❌ Failed to detect Dry run failed collision error');
    return false;
  }

  if (isNotCollision) {
    console.log('❌ Incorrectly detected non-collision error as collision');
    return false;
  }

  console.log('✅ Error detection works correctly\n');

  return true;
}

async function testRetryLogic() {
  console.log('🧪 Testing Retry Logic\n');

  let attemptCount = 0;
  const maxRetries = 3;

  // Mock signAndExecuteTransaction that fails first 2 times with collision, succeeds on 3rd
  const mockSignAndExecuteTransaction = async ({ transaction }) => {
    attemptCount++;
    console.log(`📝 Mock transaction execution attempt ${attemptCount}`);
    
    if (attemptCount < 3) {
      // Simulate temp_id collision error
      throw new Error('MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)');
    }
    
    // Success on 3rd attempt
    return { digest: 'mock_digest_success', effects: [] };
  };

  // Mock createTransactionFn
  const mockCreateTransactionFn = (tempId) => {
    console.log(`🔨 Creating mock transaction with temp_id: ${tempId}`);
    return { tempId }; // Mock transaction object
  };

  try {
    const result = await MockTokenCreationService.executeWithTempIdRetry(
      mockSignAndExecuteTransaction,
      mockCreateTransactionFn,
      maxRetries,
      '0x1234567890abcdef1234567890abcdef12345678'
    );

    if (result && result.result.digest === 'mock_digest_success') {
      console.log('✅ Retry logic works correctly - succeeded after retries\n');
      return true;
    } else {
      console.log('❌ Retry logic failed - unexpected result\n');
      return false;
    }
  } catch (error) {
    console.log('❌ Retry logic failed with error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Temp ID Fix Verification Tests\n');

  try {
    const tempIdTest = await testTempIdGeneration();
    const retryTest = await testRetryLogic();

    if (tempIdTest && retryTest) {
      console.log('🎉 ALL TESTS PASSED!');
      console.log('✅ Temp ID collision fix is working correctly');
      console.log('✅ Dynamic temp_id generation produces unique values');
      console.log('✅ Error detection correctly identifies collision errors');
      console.log('✅ Retry logic handles collisions gracefully');
    } else {
      console.log('❌ SOME TESTS FAILED');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

main();
