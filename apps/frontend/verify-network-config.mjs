#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Verification script to check if all configured package IDs and object IDs exist on the network
 * This helps debug the "We were unable to locate the packageID" error
 */

// Network configuration (updated with newly deployed contracts)
const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    hopfunPackageId:
      '0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c',
    memeConfigId:
      '0xe4341287bd48105e879dfba7bc3151b5bd881efb23c6b5dbe0efefa896b0d7aa',
    hopdexPackageId:
      '0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac',
    hopdexConfigId:
      '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
    registryId:
      '0x51fdbf98bb1b5e9a574ce2c380cd0d59301db9e75484a17387d15bb5ef5d3d7d',
    registryPackageId:
      '0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e',
  },
};

async function verifyNetworkConfig() {
  console.log('🔍 Verifying network configuration...\n');

  const config = networkConfig;
  const client = new SuiClient({ url: config.rpcUrl });

  console.log(`Network: ${config.network}`);
  console.log(`RPC URL: ${config.rpcUrl}\n`);

  const checks = [
    {
      name: 'HopFun Package',
      id: config.contracts.hopfunPackageId,
      type: 'package',
    },
    {
      name: 'Meme Config Object',
      id: config.contracts.memeConfigId,
      type: 'object',
    },
    {
      name: 'HopDex Package',
      id: config.contracts.hopdexPackageId,
      type: 'package',
    },
    {
      name: 'HopDex Config Object',
      id: config.contracts.hopdexConfigId,
      type: 'object',
    },
  ];

  if (config.contracts.registryId) {
    checks.push({
      name: 'Registry Object',
      id: config.contracts.registryId,
      type: 'object',
    });
  }

  if (config.contracts.registryPackageId) {
    checks.push({
      name: 'Registry Package',
      id: config.contracts.registryPackageId,
      type: 'package',
    });
  }

  let allValid = true;

  for (const check of checks) {
    try {
      console.log(`Checking ${check.name}: ${check.id}`);

      if (check.type === 'package') {
        // Check if package exists
        const packageInfo = await client.getNormalizedMoveModulesByPackage({
          package: check.id,
        });

        if (packageInfo && Object.keys(packageInfo).length > 0) {
          console.log(
            `✅ ${check.name} exists with modules: ${Object.keys(packageInfo).join(', ')}`,
          );
        } else {
          console.log(`❌ ${check.name} exists but has no modules`);
          allValid = false;
        }
      } else {
        // Check if object exists
        const objectInfo = await client.getObject({
          id: check.id,
          options: {
            showType: true,
            showContent: true,
          },
        });

        if (objectInfo.data) {
          console.log(
            `✅ ${check.name} exists with type: ${objectInfo.data.type}`,
          );
        } else {
          console.log(`❌ ${check.name} not found`);
          allValid = false;
        }
      }
    } catch (error) {
      console.log(`❌ ${check.name} error: ${error.message}`);
      allValid = false;
    }
    console.log('');
  }

  if (allValid) {
    console.log('🎉 All configured IDs are valid!');
  } else {
    console.log(
      '⚠️  Some configured IDs are invalid. This may cause the "packageID not found" error.',
    );
    console.log('\nTo fix this:');
    console.log("1. Check if you're on the correct network");
    console.log(
      '2. Verify the package/object IDs in the network configuration',
    );
    console.log('3. Redeploy contracts if necessary');
  }

  return allValid;
}

// Test a simple transaction to see which specific ID causes the error
async function testTransactionDryRun() {
  console.log('\n🧪 Testing transaction dry run...\n');

  const config = networkConfig;
  const client = new SuiClient({ url: config.rpcUrl });

  try {
    // Test a simple call to place_dev_order to see if it fails
    const { Transaction } = await import('@mysten/sui/transactions');
    const tx = new Transaction();

    // Create a minimal place_dev_order transaction
    const [zeroCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(0)]);

    tx.moveCall({
      target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
      arguments: [
        tx.object(config.contracts.memeConfigId),
        tx.pure.u64(123), // temp_id
        zeroCoin,
      ],
    });

    // Set a dummy sender for dry run
    tx.setSender(
      '0x0000000000000000000000000000000000000000000000000000000000000001',
    );

    console.log('Testing place_dev_order transaction...');
    const dryRunResult = await client.dryRunTransactionBlock({
      transactionBlock: await tx.build({ client }),
    });

    if (dryRunResult.effects.status.status === 'success') {
      console.log('✅ place_dev_order transaction structure is valid');
    } else {
      console.log('❌ place_dev_order transaction failed:');
      console.log('Error:', dryRunResult.effects.status.error);
    }
  } catch (error) {
    console.log('❌ Transaction test failed:', error.message);

    // Check if it's a package ID error
    if (
      error.message.includes('package') ||
      error.message.includes('Package')
    ) {
      console.log('🔍 This appears to be a package ID issue');
    }
  }
}

async function main() {
  try {
    const configValid = await verifyNetworkConfig();
    await testTransactionDryRun();

    if (!configValid) {
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  }
}

main();
