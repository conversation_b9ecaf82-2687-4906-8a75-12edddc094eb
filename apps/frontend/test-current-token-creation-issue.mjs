#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to investigate the current token creation issue
 * The place_dev_order succeeded but accept_connector is failing with type mismatch
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    // NEW FIXED CONTRACT ADDRESSES
    hopfunPackageId: '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
    memeConfigId: '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
    hopdexConfigId: '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
    registryId: '0x6a8daf347e2322c1ce60f262ec408d5d89574cedd29eb6ae09777dc133d1d8c5',
    
    // OLD CONTRACT ADDRESSES (where Connector might still be created)
    oldHopfunPackageId: '0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c',
    oldMemeConfigId: '0xe4341287bd48105e879dfba7bc3151b5bd881efb23c6b5dbe0efefa896b0d7aa',
  },
};

async function investigateCurrentIssue() {
  console.log('🔍 Investigating Current Token Creation Issue\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  console.log('📋 Configuration:');
  console.log(`  NEW HopFun Package: ${config.hopfunPackageId}`);
  console.log(`  NEW MemeConfig: ${config.memeConfigId}`);
  console.log(`  OLD HopFun Package: ${config.oldHopfunPackageId}`);
  console.log(`  OLD MemeConfig: ${config.oldMemeConfigId}\n`);

  // Step 1: Check registry configuration
  console.log('🔍 Step 1: Checking registry configuration...');
  try {
    const registryObject = await client.getObject({
      id: config.registryId,
      options: {
        showContent: true,
        showType: true
      }
    });
    
    if (registryObject.data && registryObject.data.content) {
      const fields = registryObject.data.content.fields;
      console.log('✅ Registry configuration:');
      console.log(`   meme_config_address: ${fields.meme_config_address}`);
      console.log(`   hopfun_package_id: ${fields.hopfun_package_id}`);
      
      if (fields.meme_config_address === config.memeConfigId) {
        console.log('✅ Registry points to NEW MemeConfig');
      } else if (fields.meme_config_address === config.oldMemeConfigId) {
        console.log('❌ Registry still points to OLD MemeConfig');
        console.log('🚨 THIS IS THE ISSUE: Registry not updated properly');
      } else {
        console.log('⚠️  Registry points to unknown MemeConfig:', fields.meme_config_address);
      }
      
      if (fields.hopfun_package_id === config.hopfunPackageId) {
        console.log('✅ Registry points to NEW HopFun Package');
      } else {
        console.log('❌ Registry points to different HopFun Package:', fields.hopfun_package_id);
      }
    } else {
      console.log('❌ Registry object not found or has no content');
    }
  } catch (error) {
    console.log('❌ Error checking registry:', error.message);
  }

  // Step 2: Check for Connector objects in both OLD and NEW MemeConfig
  console.log('\n🔍 Step 2: Checking for Connector objects...');
  
  // Check OLD MemeConfig
  try {
    console.log('🔍 Checking OLD MemeConfig for Connector objects...');
    const oldMemeConfigObjects = await client.getOwnedObjects({
      owner: config.oldMemeConfigId,
      options: {
        showType: true,
        showContent: true
      }
    });
    
    const oldConnectorObjects = oldMemeConfigObjects.data.filter(obj => 
      obj.data?.type?.includes('::connector::Connector')
    );
    
    console.log(`📦 Found ${oldConnectorObjects.length} Connector objects in OLD MemeConfig`);
    
    if (oldConnectorObjects.length > 0) {
      console.log('🔗 OLD Connector objects:');
      oldConnectorObjects.forEach((obj, index) => {
        console.log(`   ${index + 1}. Object ID: ${obj.data.objectId}`);
        console.log(`      Type: ${obj.data.type}`);
        console.log(`      Package: ${obj.data.type.split('::')[0]}`);
      });
    }
  } catch (error) {
    console.log('❌ Error checking OLD MemeConfig:', error.message);
  }
  
  // Check NEW MemeConfig
  try {
    console.log('\n🔍 Checking NEW MemeConfig for Connector objects...');
    const newMemeConfigObjects = await client.getOwnedObjects({
      owner: config.memeConfigId,
      options: {
        showType: true,
        showContent: true
      }
    });
    
    const newConnectorObjects = newMemeConfigObjects.data.filter(obj => 
      obj.data?.type?.includes('::connector::Connector')
    );
    
    console.log(`📦 Found ${newConnectorObjects.length} Connector objects in NEW MemeConfig`);
    
    if (newConnectorObjects.length > 0) {
      console.log('🔗 NEW Connector objects:');
      newConnectorObjects.forEach((obj, index) => {
        console.log(`   ${index + 1}. Object ID: ${obj.data.objectId}`);
        console.log(`      Type: ${obj.data.type}`);
        console.log(`      Package: ${obj.data.type.split('::')[0]}`);
      });
    }
  } catch (error) {
    console.log('❌ Error checking NEW MemeConfig:', error.message);
  }

  // Step 3: Analyze the issue
  console.log('\n🔍 Step 3: Issue Analysis...');
  console.log('');
  console.log('📊 CURRENT SITUATION:');
  console.log('   1. ✅ Third transaction (place_dev_order) succeeded');
  console.log('   2. ❌ Fourth transaction (accept_connector) failing with type mismatch');
  console.log('   3. 🔍 Error: CommandArgumentError { arg_idx: 2, kind: TypeMismatch }');
  console.log('');
  console.log('🚨 LIKELY CAUSES:');
  console.log('   A. Registry still points to OLD MemeConfig');
  console.log('      - create_connector uses OLD contract');
  console.log('      - Creates Connector with OLD type');
  console.log('      - accept_connector expects NEW type');
  console.log('');
  console.log('   B. Frontend uses cached/wrong contract addresses');
  console.log('      - create_connector called with OLD package ID');
  console.log('      - accept_connector called with NEW package ID');
  console.log('');
  console.log('   C. Mixed contract usage in token creation flow');
  console.log('      - Some steps use OLD contracts');
  console.log('      - Some steps use NEW contracts');
  console.log('');
  console.log('💡 SOLUTION APPROACHES:');
  console.log('   1. 🔄 Verify registry is properly updated');
  console.log('   2. 🧹 Clear any cached contract addresses');
  console.log('   3. 🔍 Ensure all steps use consistent contract addresses');
  console.log('   4. 🚀 Start fresh token creation (not continuing old attempts)');
  console.log('');
  console.log('🎯 IMMEDIATE ACTIONS:');
  console.log('   1. Check if registry update was successful');
  console.log('   2. Verify frontend uses correct contract addresses');
  console.log('   3. Ensure create_connector uses NEW contract');
  console.log('   4. Test with completely new token creation');
}

async function main() {
  try {
    await investigateCurrentIssue();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
