#!/usr/bin/env node

/**
 * Test script to verify the compilation API works correctly
 * This tests the /contract-compilation/compile endpoint
 */

async function testCompilationAPI() {
  console.log('🧪 Testing Compilation API\n');
  
  const API_BASE_URL = 'http://localhost:3001'; // Adjust if needed
  const COMPILE_ENDPOINT = `${API_BASE_URL}/contract-compilation/compile`;
  
  console.log('📋 TEST CONFIGURATION:');
  console.log(`  API Base URL: ${API_BASE_URL}`);
  console.log(`  Compile Endpoint: ${COMPILE_ENDPOINT}\n`);
  
  // Test payload
  const testPayload = {
    metadata: {
      name: "Test Token",
      symbol: "TEST",
      description: "A test token for compilation testing",
      imageUrl: "https://example.com/test.png",
      totalSupply: "1000000000",
      decimals: 9,
      twitter: "https://twitter.com/test",
      website: "https://test.com",
      telegram: "https://t.me/test"
    },
    network: "DEVNET",
    templateVersion: "latest"
  };
  
  console.log('📝 TEST PAYLOAD:');
  console.log(JSON.stringify(testPayload, null, 2));
  console.log('');
  
  try {
    console.log('🚀 Sending compilation request...');
    
    const response = await fetch(COMPILE_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
    
    const responseData = await response.json();
    
    if (response.ok) {
      console.log('✅ COMPILATION SUCCESS!');
      console.log('');
      console.log('📦 Response Data:');
      console.log(`  Success: ${responseData.success}`);
      console.log(`  Message: ${responseData.message}`);
      
      if (responseData.data) {
        console.log('');
        console.log('🔍 Compilation Results:');
        console.log(`  Module Name: ${responseData.data.moduleName}`);
        console.log(`  Compilation ID: ${responseData.data.compilationId}`);
        console.log(`  Bytecode Size: ${responseData.data.bytecode ? responseData.data.bytecode.length : 'N/A'} characters`);
        console.log(`  Source Code Size: ${responseData.data.sourceCode ? responseData.data.sourceCode.length : 'N/A'} characters`);
        
        if (responseData.data.sourceCode) {
          console.log('');
          console.log('📝 Generated Source Code (first 500 chars):');
          console.log(responseData.data.sourceCode.substring(0, 500) + '...');
        }
        
        if (responseData.data.bytecode) {
          console.log('');
          console.log('💾 Bytecode Generated:');
          console.log(`  Base64 Length: ${responseData.data.bytecode.length}`);
          console.log(`  First 100 chars: ${responseData.data.bytecode.substring(0, 100)}...`);
        }
      }
      
      console.log('');
      console.log('🎉 COMPILATION TEST PASSED!');
      console.log('   ✅ No COMPILATION_FAILED error');
      console.log('   ✅ Bytecode successfully generated');
      console.log('   ✅ HopFun connector import resolved');
      console.log('   ✅ Move.toml dependencies satisfied');
      
    } else {
      console.log('❌ COMPILATION FAILED!');
      console.log('');
      console.log('📊 Error Response:');
      console.log(JSON.stringify(responseData, null, 2));
      
      if (responseData.error) {
        console.log('');
        console.log('🚨 Error Details:');
        console.log(`  Code: ${responseData.error.code}`);
        console.log(`  Message: ${responseData.error.message}`);
        
        if (responseData.error.details) {
          console.log('  Details:', JSON.stringify(responseData.error.details, null, 2));
        }
        
        // Check if it's the specific compilation error we're trying to fix
        if (responseData.error.code === 'COMPILATION_FAILED' && 
            responseData.error.message.includes('Dependencies on Bridge, MoveStdlib, Sui')) {
          console.log('');
          console.log('🔍 ANALYSIS: This is the dependency injection error we fixed!');
          console.log('   The fix may not have been applied correctly or the server needs restart.');
        }
      }
    }
    
  } catch (error) {
    console.log('❌ REQUEST FAILED!');
    console.log('');
    console.log('🚨 Error Details:');
    console.log(`  Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('🔍 ANALYSIS: Server connection refused');
      console.log('   Make sure the server is running on the correct port');
      console.log('   Try: npm run dev (in the server directory)');
    }
  }
  
  console.log('');
  console.log('📋 NEXT STEPS:');
  console.log('');
  console.log('If compilation succeeded:');
  console.log('  ✅ The Move.toml fix is working correctly');
  console.log('  ✅ HopFun connector imports are resolved');
  console.log('  ✅ Ready to test complete token creation flow');
  console.log('');
  console.log('If compilation failed:');
  console.log('  🔄 Check if server needs restart to pick up changes');
  console.log('  🔍 Verify Move.toml generation includes all dependencies');
  console.log('  🛠️  Check server logs for detailed error information');
  console.log('');
  console.log('🚀 COMPLETE SYSTEM STATUS:');
  console.log('  1. ✅ Registry configuration updated');
  console.log('  2. ✅ temp_id collision resolved');
  console.log('  3. ✅ Type mismatch resolved');
  console.log('  4. 🧪 Compilation failure fix (testing now)');
}

async function main() {
  try {
    await testCompilationAPI();
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

main();
