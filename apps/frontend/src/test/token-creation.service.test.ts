import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TokenCreationService } from '../services/token-creation.service';

describe('TokenCreationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateUniqueTempId', () => {
    it('should generate unique temp_ids', () => {
      const userAddress = '0x1234567890abcdef1234567890abcdef12345678';
      const tempIds = new Set<number>();
      const numTests = 100;

      for (let i = 0; i < numTests; i++) {
        const tempId = TokenCreationService.generateUniqueTempId(userAddress);
        expect(tempIds.has(tempId)).toBe(false);
        expect(tempId).toBeGreaterThan(0);
        expect(tempId).toBeLessThan(Number.MAX_SAFE_INTEGER);
        tempIds.add(tempId);
      }

      expect(tempIds.size).toBe(numTests);
    });

    it('should generate different temp_ids for different users', () => {
      const user1 = '0x1111111111111111111111111111111111111111';
      const user2 = '0x2222222222222222222222222222222222222222';
      
      const user1TempIds = [];
      const user2TempIds = [];
      
      for (let i = 0; i < 10; i++) {
        user1TempIds.push(TokenCreationService.generateUniqueTempId(user1));
        user2TempIds.push(TokenCreationService.generateUniqueTempId(user2));
      }

      // Check if there's any overlap (there shouldn't be)
      const overlap = user1TempIds.filter(id => user2TempIds.includes(id));
      expect(overlap.length).toBe(0);
    });

    it('should handle missing user address gracefully', () => {
      const tempId = TokenCreationService.generateUniqueTempId();
      expect(tempId).toBeGreaterThan(0);
      expect(tempId).toBeLessThan(Number.MAX_SAFE_INTEGER);
    });
  });

  describe('isTempIdCollisionError', () => {
    it('should detect MoveAbort collision errors', () => {
      const collisionError = new Error(
        'MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)'
      );
      
      expect(TokenCreationService.isTempIdCollisionError(collisionError)).toBe(true);
    });

    it('should detect Dry run failed collision errors', () => {
      const collisionError = new Error(
        'Dry run failed, could not automatically determine a budget: MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)'
      );
      
      expect(TokenCreationService.isTempIdCollisionError(collisionError)).toBe(true);
    });

    it('should not detect non-collision errors', () => {
      const nonCollisionError = new Error('User rejection | (UserRejectionError:-4005)');
      expect(TokenCreationService.isTempIdCollisionError(nonCollisionError)).toBe(false);

      const networkError = new Error('Network error');
      expect(TokenCreationService.isTempIdCollisionError(networkError)).toBe(false);

      const otherMoveAbort = new Error('MoveAbort(..., 5)'); // Different error code
      expect(TokenCreationService.isTempIdCollisionError(otherMoveAbort)).toBe(false);
    });
  });

  describe('executeWithTempIdRetry', () => {
    it('should succeed on first attempt when no collision', async () => {
      const mockSignAndExecuteTransaction = vi.fn().mockResolvedValue({
        digest: 'success_digest',
        effects: []
      });

      const mockCreateTransactionFn = vi.fn().mockReturnValue({ tempId: 123 });

      const result = await TokenCreationService.executeWithTempIdRetry(
        mockSignAndExecuteTransaction,
        mockCreateTransactionFn,
        3,
        '0x1234567890abcdef1234567890abcdef12345678'
      );

      expect(result.result.digest).toBe('success_digest');
      expect(result.tempId).toBeGreaterThan(0);
      expect(mockSignAndExecuteTransaction).toHaveBeenCalledTimes(1);
      expect(mockCreateTransactionFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on temp_id collision and succeed', async () => {
      let attemptCount = 0;
      const mockSignAndExecuteTransaction = vi.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)');
        }
        return Promise.resolve({ digest: 'success_after_retry', effects: [] });
      });

      const mockCreateTransactionFn = vi.fn().mockReturnValue({ tempId: 123 });

      const result = await TokenCreationService.executeWithTempIdRetry(
        mockSignAndExecuteTransaction,
        mockCreateTransactionFn,
        3,
        '0x1234567890abcdef1234567890abcdef12345678'
      );

      expect(result.result.digest).toBe('success_after_retry');
      expect(mockSignAndExecuteTransaction).toHaveBeenCalledTimes(3);
      expect(mockCreateTransactionFn).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries with collision errors', async () => {
      const mockSignAndExecuteTransaction = vi.fn().mockRejectedValue(
        new Error('MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)')
      );

      const mockCreateTransactionFn = vi.fn().mockReturnValue({ tempId: 123 });

      await expect(
        TokenCreationService.executeWithTempIdRetry(
          mockSignAndExecuteTransaction,
          mockCreateTransactionFn,
          3,
          '0x1234567890abcdef1234567890abcdef12345678'
        )
      ).rejects.toThrow('MoveAbort');

      expect(mockSignAndExecuteTransaction).toHaveBeenCalledTimes(3);
      expect(mockCreateTransactionFn).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-collision errors', async () => {
      const mockSignAndExecuteTransaction = vi.fn().mockRejectedValue(
        new Error('User rejection | (UserRejectionError:-4005)')
      );

      const mockCreateTransactionFn = vi.fn().mockReturnValue({ tempId: 123 });

      await expect(
        TokenCreationService.executeWithTempIdRetry(
          mockSignAndExecuteTransaction,
          mockCreateTransactionFn,
          3,
          '0x1234567890abcdef1234567890abcdef12345678'
        )
      ).rejects.toThrow('User rejection');

      expect(mockSignAndExecuteTransaction).toHaveBeenCalledTimes(1);
      expect(mockCreateTransactionFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('getErrorMessage', () => {
    it('should return friendly message for temp_id collision', () => {
      const collisionError = new Error(
        'MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3)'
      );
      
      const message = TokenCreationService.getErrorMessage(collisionError);
      expect(message).toBe('Token creation temporarily unavailable due to high demand. Please try again in a few seconds.');
    });

    it('should return original message for other errors', () => {
      const userRejectionError = new Error('User rejection | (UserRejectionError:-4005)');
      const message = TokenCreationService.getErrorMessage(userRejectionError);
      expect(message).toBe('Transaction was cancelled by user');
    });
  });
});
