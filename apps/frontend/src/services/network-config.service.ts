import { getFullnodeUrl } from '@mysten/sui/client';

export type Network = 'devnet' | 'testnet' | 'mainnet';

interface ContractConfig {
  hopfunPackageId: string;
  memeConfigId: string;
  hopfunAdminCap: string;
  hopdexPackageId: string;
  hopdexConfigId: string;
  registryId?: string; // Config Registry ID (optional for backward compatibility)
  registryPackageId?: string; // Config Registry Package ID
  useRegistry?: boolean; // Flag to indicate if registry should be used
}

interface NetworkConfig {
  network: Network;
  rpcUrl: string;
  contracts: ContractConfig;
}

class NetworkConfigService {
  private static instance: NetworkConfigService;
  private currentNetwork: Network;

  private constructor() {
    const envNetwork = process.env.NEXT_PUBLIC_NETWORK ?? 'devnet';
    this.currentNetwork = this.validateNetwork(envNetwork);
  }

  static getInstance(): NetworkConfigService {
    NetworkConfigService.instance = new NetworkConfigService();
    return NetworkConfigService.instance;
  }

  private validateNetwork(network: string): Network {
    const validNetworks: Network[] = ['devnet', 'testnet', 'mainnet'];
    if (validNetworks.includes(network as Network)) {
      return network as Network;
    }
    console.warn(`Invalid network: ${network}, defaulting to devnet`);
    return 'devnet';
  }

  getCurrentNetwork(): Network {
    return this.currentNetwork;
  }

  setNetwork(network: Network): void {
    this.currentNetwork = network;
  }

  getNetworkConfig(): NetworkConfig {
    const network = this.getCurrentNetwork();
    const contracts = this.getContractConfig(network);
    const rpcUrl = this.getRpcUrl(network);

    return {
      network,
      rpcUrl,
      contracts,
    };
  }

  private getRpcUrl(network: Network): string {
    // Use the SUI SDK's built-in RPC URLs
    return getFullnodeUrl(network);
  }

  private getContractConfig(network: Network): ContractConfig {
    switch (network) {
      case 'devnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET ??
            '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
          memeConfigId:
            process.env.NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET ??
            '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_DEVNET ??
            '0x5fe8ffe3fe749ccfcb6b768dae5c3ed076ff8d309603a6abebfc1e719c7ce63e',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_DEVNET ??
            '0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_DEVNET ??
            '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
          registryId:
            process.env.NEXT_PUBLIC_REGISTRY_ID_DEVNET ??
            '0x6a8daf347e2322c1ce60f262ec408d5d89574cedd29eb6ae09777dc133d1d8c5',
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET ??
            '0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e',
          useRegistry: process.env.NEXT_PUBLIC_USE_REGISTRY === 'true',
        };

      case 'testnet':
        // Return devnet values for testnet as fallback for now
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_TESTNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_TESTNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_TESTNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_TESTNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_TESTNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_TESTNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_TESTNET ?? undefined,
          useRegistry: false,
        };

      case 'mainnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_MAINNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_MAINNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_MAINNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_MAINNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_MAINNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_MAINNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_MAINNET ?? undefined,
          useRegistry: false,
        };

      default:
        throw new Error(`Unsupported network: ${network as string}`);
    }
  }

  isProductionNetwork(): boolean {
    return this.currentNetwork === 'mainnet';
  }

  isDevelopmentNetwork(): boolean {
    return (
      this.currentNetwork === 'devnet' || this.currentNetwork === 'testnet'
    );
  }
}

export const networkConfigService = NetworkConfigService.getInstance();
