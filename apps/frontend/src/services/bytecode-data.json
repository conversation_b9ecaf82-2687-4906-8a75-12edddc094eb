{"bytecode": "oRzrCwYAAAALAQAWAhY8A1JNBJ8BEAWvAbEBB+AChQMI5QWgAQaFB2YK6wcbC4YIAgyICHkAJAMeBA4BHAEhAgwCDQIbAiUCJwIoAAcCAAAECAEAAQEDCAADBQcBAAAEBgcABQAEAQABBgEMAQABBgIMAQABBggMAQABBwoEAAkJAgAKCwcAABUAAQAADwIBAAIZGAEBAAMgBgcBAAQpBBcABhAJCgECBhYNEgEABhcMDQEABxIRAQAHGBARAAgdBgEBDAglFAEBCAkfDg8AChoEBQADBQUICgsHCAYICxMKFgIIAggABwgKAAMLAQEIAAYIAgcICgQFCwcBCAALBgEIAAsIAQgAAQoCAQgLAQkAAQsDAQkAAQgABwkAAgoCCgIKAgsDAQgLBwgKAgsIAQkACwcBCQABCwcBCAADBwsIAQkAAwcICgELBgEJAAEGCAoBBQEHCAoBCAkBCwUBCQABCwEBCAACCQAFBAULBQEIAAMLCAEIAAELCAEIAAEIBAgDCwUBCQAIBAgECAQFBggCBwgKB0JhbGFuY2UEQ29pbgxDb2luTWV0YWRhdGEOQ29uZmlnUmVnaXN0cnkOQ3VycmVuY3lIb2xkZXIGT3B0aW9uBlN0cmluZwhURU1QTEFURQtUcmVhc3VyeUNhcAlUeENvbnRleHQDVUlEA1VybAdiYWxhbmNlBGNvaW4JY29ubmVjdG9yEGNyZWF0ZV9jb25uZWN0b3IPY3JlYXRlX2N1cnJlbmN5B2NyZWF0b3IGZGVsZXRlC2R1bW15X2ZpZWxkAmlkBGluaXQMaW50b19iYWxhbmNlBG1pbnQDbmV3D25ld19mcm9tX3N1cHBseRVuZXdfdW5zYWZlX2Zyb21fYnl0ZXMGb2JqZWN0Bm9wdGlvbhRwdWJsaWNfZnJlZXplX29iamVjdAhyZWdpc3RyeQZzZW5kZXIEc29tZQZzdHJpbmcGc3VwcGx5B3RlbXBfaWQIdGVtcGxhdGUIdHJhbnNmZXIMdHJlYXN1cnlfY2FwCnR4X2NvbnRleHQDdXJsBHV0ZjgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJGYii0cGprH35JP2fv0p3uzHgWFvlstKdCQH6ne5r7fqUxIp/O8VjV9gLTdtPEer/YP4NLRFmmkFBTpajhjFY8CgIJCEltYWdlVXJsAgEGCgIIB1R3aXR0ZXIKAggHV2Vic2l0ZQoCCQhUZWxlZ3JhbQoCBQROYW1lCgIHBlN5bWJvbAoCDAtEZXNjcmlwdGlvbgMIewAAAAAAAAADCACAxqR+jQMAAAIBEwEBAgUUCAkmCwgBCQAiCwUBCQAjAxEFAQgAAAAAAyILAAcBBwYHBQcHBwARDTgACgE4AQwDDAULAzgCDQUHCQoBOAMMBAoBLhEMDAILAREJCwULBDgEBwgKAjkACwI4BQIBAQQAFRYLADoADAMMBQwEDAYRCAsGOAYLBQsEBwIRBAcDEQQHBBEECwMLAQsCOAcCAA==", "dependencies": ["0x0000000000000000000000000000000000000000000000000000000000000001", "0x0000000000000000000000000000000000000000000000000000000000000002", "0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e", "0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac", "0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c"], "metadata": {"name": "TEMPLATE", "symbol": "TEMPLATE", "description": "Template coin with registry support", "iconUrl": "ImageUrl", "totalSupply": "1000000000000000", "decimals": 6, "usesRegistry": true, "version": "2.0.0"}, "checksum": "==", "generatedAt": "2025-08-19T10:07:43.705Z"}