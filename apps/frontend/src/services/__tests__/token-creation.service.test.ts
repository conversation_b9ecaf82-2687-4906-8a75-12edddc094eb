import { TokenCreationService } from '../token-creation.service';

describe('TokenCreationService', () => {
  describe('generateUniqueTempId', () => {
    it('should generate safe integers within JavaScript limits', () => {
      // Test multiple generations to ensure consistency
      const testResults = [];
      for (let i = 0; i < 100; i++) {
        const tempId = TokenCreationService.generateUniqueTempId();
        testResults.push(tempId);
        
        // Verify it's a safe integer
        expect(Number.isSafeInteger(tempId)).toBe(true);
        
        // Verify it's positive
        expect(tempId).toBeGreaterThan(0);
        
        // Verify it's well within u64 limits (18,446,744,073,709,551,615)
        expect(tempId).toBeLessThan(18446744073709551615);
        
        // Verify it's well within JavaScript safe integer limits
        expect(tempId).toBeLessThan(Number.MAX_SAFE_INTEGER);
        
        // Verify it's in our expected range (under 100 billion for ultra-safety)
        expect(tempId).toBeLessThan(100000000000); // 100 billion
      }
      
      // Verify uniqueness (at least most should be unique)
      const uniqueValues = new Set(testResults);
      expect(uniqueValues.size).toBeGreaterThan(testResults.length * 0.9); // At least 90% unique
    });

    it('should generate different values with user address entropy', () => {
      const userAddress1 = '0x1234567890abcdef1234567890abcdef12345678';
      const userAddress2 = '0xfedcba0987654321fedcba0987654321fedcba09';
      
      const tempId1 = TokenCreationService.generateUniqueTempId(userAddress1);
      const tempId2 = TokenCreationService.generateUniqueTempId(userAddress2);
      
      // Both should be safe integers
      expect(Number.isSafeInteger(tempId1)).toBe(true);
      expect(Number.isSafeInteger(tempId2)).toBe(true);
      
      // They should be different (very high probability)
      expect(tempId1).not.toBe(tempId2);
    });

    it('should handle invalid user addresses gracefully', () => {
      const invalidAddress = 'invalid-address';
      
      const tempId = TokenCreationService.generateUniqueTempId(invalidAddress);
      
      // Should still generate a valid safe integer
      expect(Number.isSafeInteger(tempId)).toBe(true);
      expect(tempId).toBeGreaterThan(0);
      expect(tempId).toBeLessThan(Number.MAX_SAFE_INTEGER);
    });

    it('should work without user address', () => {
      const tempId = TokenCreationService.generateUniqueTempId();
      
      // Should still generate a valid safe integer
      expect(Number.isSafeInteger(tempId)).toBe(true);
      expect(tempId).toBeGreaterThan(0);
      expect(tempId).toBeLessThan(Number.MAX_SAFE_INTEGER);
    });

    it('should generate values in expected range for u64 compatibility', () => {
      const testResults = [];
      for (let i = 0; i < 50; i++) {
        const tempId = TokenCreationService.generateUniqueTempId();
        testResults.push(tempId);
      }
      
      // All values should be in our ultra-safe range
      const maxGenerated = Math.max(...testResults);
      const minGenerated = Math.min(...testResults);
      
      // Should be in billions range (safe for u64 and JavaScript)
      expect(maxGenerated).toBeLessThan(100000000000); // Less than 100 billion
      expect(minGenerated).toBeGreaterThan(1000000); // Greater than 1 million
      
      // Ratio to MAX_SAFE_INTEGER should be very small
      const maxRatio = maxGenerated / Number.MAX_SAFE_INTEGER;
      expect(maxRatio).toBeLessThan(0.00001); // Less than 0.001% of safe range
    });

    it('should log generation details for debugging', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      TokenCreationService.generateUniqueTempId();
      
      // Should log the generated temp_id and validation info
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('🎲 Generated ultra-safe temp_id:')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('🔍 Temp_id validation:'),
        expect.objectContaining({
          value: expect.any(Number),
          withinSafeInteger: true,
          maxSafeInteger: Number.MAX_SAFE_INTEGER,
          ratio: expect.any(Number)
        })
      );
      
      consoleSpy.mockRestore();
    });
  });
});
