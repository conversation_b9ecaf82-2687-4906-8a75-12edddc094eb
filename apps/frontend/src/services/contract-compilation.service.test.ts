import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import {
  ContractCompilationError,
  ContractCompilationService,
  type ContractMetadata,
} from './contract-compilation.service';
import type { TokenCreationPayload } from './token-creation.service';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('ContractCompilationService', () => {
  const mockApiUrl = 'http://localhost:3001';

  beforeEach(() => {
    vi.clearAllMocks();
    // Set environment variable for tests
    process.env.NEXT_PUBLIC_API_URL = mockApiUrl;
  });

  afterEach(() => {
    delete process.env.NEXT_PUBLIC_API_URL;
  });

  const validMetadata: ContractMetadata = {
    name: 'Test Token',
    symbol: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: 'https://twitter.com/test',
    website: 'https://example.com',
    telegram: 'https://t.me/test',
    totalSupply: 1000000,
    decimals: 6,
  };

  const validTokenPayload: TokenCreationPayload = {
    name: 'Test Token',
    symbol: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: 'https://twitter.com/test',
    website: 'https://example.com',
    telegram: 'https://t.me/test',
    totalSupply: 1000000,
    decimals: 6,
  };

  describe('tokenPayloadToMetadata', () => {
    it('should convert TokenCreationPayload to ContractMetadata', () => {
      const metadata =
        ContractCompilationService.tokenPayloadToMetadata(validTokenPayload);

      expect(metadata).toEqual({
        name: 'Test Token',
        symbol: 'TEST',
        description: 'A test token',
        imageUrl: 'https://example.com/image.png',
        twitter: 'https://twitter.com/test',
        website: 'https://example.com',
        telegram: 'https://t.me/test',
        totalSupply: 1000000,
        decimals: 6,
      });
    });

    it('should handle optional fields correctly', () => {
      const minimalPayload: TokenCreationPayload = {
        name: 'Minimal Token',
        symbol: 'MIN',
        totalSupply: 1000,
        decimals: 0,
      };

      const metadata =
        ContractCompilationService.tokenPayloadToMetadata(minimalPayload);

      expect(metadata).toEqual({
        name: 'Minimal Token',
        symbol: 'MIN',
        description: undefined,
        imageUrl: undefined,
        twitter: undefined,
        website: undefined,
        telegram: undefined,
        totalSupply: 1000,
        decimals: 0,
      });
    });
  });

  describe('generateSource', () => {
    it('should generate source code successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Contract source generated successfully',
        data: {
          sourceCode: 'module coin_template::test { /* mock source */ }',
          moduleName: 'test_12345678',
          metadata: validMetadata,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result =
        await ContractCompilationService.generateSource(validMetadata);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        `${mockApiUrl}/api/contract-compilation/generate`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(validMetadata),
        },
      );
    });

    it('should handle API errors', async () => {
      const mockErrorResponse = {
        error: {
          code: 'INVALID_METADATA',
          message: 'Invalid token metadata',
          details: { errors: ['Symbol too long'] },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => mockErrorResponse,
      });

      await expect(
        ContractCompilationService.generateSource(validMetadata),
      ).rejects.toThrow(ContractCompilationError);

      try {
        await ContractCompilationService.generateSource(validMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ContractCompilationError);
        expect((error as ContractCompilationError).code).toBe(
          'INVALID_METADATA',
        );
        expect((error as ContractCompilationError).message).toBe(
          'Invalid token metadata',
        );
      }
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(
        ContractCompilationService.generateSource(validMetadata),
      ).rejects.toThrow(ContractCompilationError);

      try {
        await ContractCompilationService.generateSource(validMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ContractCompilationError);
        expect((error as ContractCompilationError).code).toBe('NETWORK_ERROR');
      }
    });
  });

  describe('compileContract', () => {
    const compileRequest = {
      metadata: validMetadata,
      network: 'TESTNET' as const,
      templateVersion: 'latest',
    };

    it('should compile contract successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Contract compiled successfully',
        data: {
          bytecode: 'base64-encoded-bytecode',
          dependencies: ['0x1::string', '0x2::coin'],
          sourceCode: 'module coin_template::test { /* mock */ }',
          moduleName: 'test_12345678',
          metadata: validMetadata,
          compilationId: 'compilation-123',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result =
        await ContractCompilationService.compileContract(compileRequest);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        `${mockApiUrl}/api/contract-compilation/compile`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(compileRequest),
        },
      );
    });

    it('should handle compilation errors', async () => {
      const mockErrorResponse = {
        error: {
          code: 'COMPILATION_FAILED',
          message: 'Compilation failed',
          details: { originalError: 'Syntax error' },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => mockErrorResponse,
      });

      await expect(
        ContractCompilationService.compileContract(compileRequest),
      ).rejects.toThrow(ContractCompilationError);
    });
  });

  describe('getCompilerStatus', () => {
    it('should get compiler status successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Compiler status retrieved',
        data: {
          available: true,
          version: 'sui 1.0.0',
          status: 'ready' as const,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await ContractCompilationService.getCompilerStatus();

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        `${mockApiUrl}/api/contract-compilation/compiler/status`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });
  });

  describe('compileTokenContract', () => {
    it('should compile token contract successfully', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Contract compiled successfully',
        data: {
          bytecode: 'base64-encoded-bytecode',
          dependencies: ['0x1::string', '0x2::coin'],
          sourceCode: 'module coin_template::test { /* mock */ }',
          moduleName: 'test_12345678',
          metadata: validMetadata,
          compilationId: 'compilation-123',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result =
        await ContractCompilationService.compileTokenContract(
          validTokenPayload,
        );

      expect(result).toEqual({
        bytecode: 'base64-encoded-bytecode',
        dependencies: ['0x1::string', '0x2::coin'],
        sourceCode: 'module coin_template::test { /* mock */ }',
        moduleName: 'test_12345678',
        compilationId: 'compilation-123',
      });
    });

    it('should handle compilation failures', async () => {
      const mockResponse = {
        status: 'error' as const,
        message: 'Compilation failed',
        error: {
          code: 'COMPILATION_FAILED',
          details: { originalError: 'Syntax error' },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await expect(
        ContractCompilationService.compileTokenContract(validTokenPayload),
      ).rejects.toThrow(ContractCompilationError);
    });
  });

  describe('validateCompilerAvailability', () => {
    it('should return true when compiler is available', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Compiler status retrieved',
        data: {
          available: true,
          version: 'sui 1.0.0',
          status: 'ready' as const,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result =
        await ContractCompilationService.validateCompilerAvailability();

      expect(result).toBe(true);
    });

    it('should return false when compiler is unavailable', async () => {
      const mockResponse = {
        status: 'success' as const,
        message: 'Compiler status retrieved',
        data: {
          available: false,
          version: 'unknown',
          status: 'unavailable' as const,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result =
        await ContractCompilationService.validateCompilerAvailability();

      expect(result).toBe(false);
    });

    it('should return false on network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result =
        await ContractCompilationService.validateCompilerAvailability();

      expect(result).toBe(false);
    });
  });
});
