import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';

import { BytecodeTemplateService } from './bytecode-template.service';
import { ContractCompilationService } from './contract-compilation.service';
import { networkConfigService } from './network-config.service';

// Error types for better error handling
export class TokenCreationError extends Error {
  constructor(
    message: string,
    public code:
      | 'WALLET_NOT_CONNECTED'
      | 'INVALID_NETWORK'
      | 'BYTECODE_ERROR'
      | 'TRANSACTION_FAILED'
      | 'VALIDATION_ERROR'
      | 'NETWORK_ERROR'
      | 'TRANSACTION_NOT_FOUND'
      | 'COMPILATION_FAILED',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public details?: any,
  ) {
    super(message);
    this.name = 'TokenCreationError';
  }
}

export interface TokenCreationPayload {
  name: string;
  symbol: string;
  description?: string;
  imageUrl?: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  totalSupply: number;
  decimals: number;
}

export interface TokenCreationResult {
  success: boolean;
  packageId?: string;
  digest?: string;
  error?: TokenCreationError;
}

export interface BondingCurveCreationResult {
  success: boolean;
  bondingCurveId?: string;
  digest?: string;
  error?: TokenCreationError;
}

export class TokenCreationService {
  private static suiClient: SuiClient | null = null;

  /**
   * Initialize the SUI client with the current network configuration
   */
  static initializeClient(): SuiClient {
    const config = networkConfigService.getNetworkConfig();
    TokenCreationService.suiClient ??= new SuiClient({ url: config.rpcUrl });
    return TokenCreationService.suiClient;
  }

  /**
   * Create a transaction to publish a new coin module using dynamic compilation
   */
  static async createPublishCoinTransactionDynamic(
    payload: TokenCreationPayload,
    sender: string,
  ): Promise<Transaction> {
    const tx = new Transaction();

    // Validate the payload first
    const validation = this.validatePayload(payload);
    if (!validation.valid) {
      throw new TokenCreationError(
        `Validation failed: ${validation.errors.join(', ')}`,
        'VALIDATION_ERROR',
        validation.errors,
      );
    }

    try {
      // Check if compiler is available
      const isCompilerAvailable =
        await ContractCompilationService.validateCompilerAvailability();
      if (!isCompilerAvailable) {
        console.warn(
          '⚠️ Dynamic compilation not available, falling back to template',
        );
        return this.createPublishCoinTransaction(payload, sender);
      }

      // Attempt dynamic compilation
      console.log('🔨 Using dynamic compilation...');

      try {
        const compilationResult =
          await ContractCompilationService.compileTokenContract(
            payload,
            'DEVNET', // TODO: Make this configurable based on network
          );

        console.log('✅ Dynamic compilation successful:', {
          moduleName: compilationResult.moduleName,
          compilationId: compilationResult.compilationId,
          bytecodeLength: compilationResult.bytecode.length,
        });

        // Convert base64 bytecode to Uint8Array for publishing
        const bytecodeBase64 = compilationResult.bytecode;
        const dependencies = compilationResult.dependencies;

        // Publish the dynamically compiled coin module
        const [upgradeCap] = tx.publish({
          modules: [bytecodeBase64], // SDK expects base64 strings
          dependencies: dependencies,
        });

        // Transfer the upgrade capability to the sender
        tx.transferObjects([upgradeCap], tx.pure.address(sender));

        // Set the sender
        tx.setSender(sender);

        return tx;
      } catch (dynamicError) {
        console.warn(
          '⚠️ Dynamic compilation failed, falling back to template:',
          dynamicError,
        );
        return this.createPublishCoinTransaction(payload, sender);
      }
    } catch (error) {
      console.error('❌ Dynamic compilation setup failed:', error);
      // Fall back to template compilation
      return this.createPublishCoinTransaction(payload, sender);
    }
  }

  /**
   * Create a transaction to publish a new coin module (legacy template method)
   */
  static createPublishCoinTransaction(
    payload: TokenCreationPayload,
    sender: string,
  ): Transaction {
    const tx = new Transaction();
    const _config = networkConfigService.getNetworkConfig();

    // Validate the payload first
    const validation = this.validatePayload(payload);
    if (!validation.valid) {
      throw new TokenCreationError(
        `Validation failed: ${validation.errors.join(', ')}`,
        'VALIDATION_ERROR',
        validation.errors,
      );
    }

    // Get the base64 bytecode directly
    // The SDK expects modules as base64 strings
    const bytecodeBase64 = BytecodeTemplateService.getBase64Bytecode();

    // Get the exact dependencies the bytecode was compiled with
    const dependencies = BytecodeTemplateService.getDependencies();

    // Debug: Log the bytecode info
    console.log('Publishing bytecode:', {
      bytecodeBase64Length: bytecodeBase64.length,
      bytecodeBase64First100: bytecodeBase64.substring(0, 100),
      dependencies: dependencies,
    });

    // Publish the coin module with the exact dependencies it was compiled with
    // This ensures compatibility with the on-chain packages
    const [upgradeCap] = tx.publish({
      modules: [bytecodeBase64], // SDK expects base64 strings
      dependencies: dependencies, // Use the exact dependencies from the bytecode
    });

    // Transfer the upgrade capability to the sender
    tx.transferObjects([upgradeCap], tx.pure.address(sender));

    // Set the sender
    tx.setSender(sender);

    return tx;
  }

  /**
   * Create a transaction to create the bonding curve for a published coin
   * This is actually a two-step process:
   * 1. place_dev_order to pay for the bonding curve creation
   * 2. accept_connector to create the bonding curve with the Connector from the published coin
   */
  /**
   * Create a transaction for creating a HopFun connector from template CurrencyHolder
   * This works with template contracts that create real HopFun Connectors
   */
  static createConnectorTransaction(
    packageId: string,
    currencyHolderId: string,
    currencyHolderObjectType: string,
  ): Transaction {
    const tx = new Transaction();
    const config = networkConfigService.getNetworkConfig();

    console.log('Creating HopFun connector:', {
      packageId,
      currencyHolderId,
      registryId: config.contracts.registryId,
      objectType: currencyHolderObjectType,
    });

    // Determine the module name based on the object type
    // Template pattern: {package}::template::CurrencyHolder<{package}::template::TEMPLATE>
    // Dynamic pattern: {package}::coin::CurrencyHolder<{package}::coin::COIN>
    const isTemplatePattern = currencyHolderObjectType.includes('::template::');
    const isDynamicPattern = currencyHolderObjectType.includes('::coin::');

    const registryId = config.contracts.registryId;
    if (!registryId) {
      throw new Error('Registry ID not configured');
    }

    if (isTemplatePattern) {
      console.log('🔧 Using template-based connector creation');
      tx.moveCall({
        target: `${packageId}::template::create_connector`,
        arguments: [tx.object(currencyHolderId), tx.object(registryId)],
        typeArguments: [],
      });
    } else if (isDynamicPattern) {
      console.log('🔧 Using dynamic compilation connector creation');
      tx.moveCall({
        target: `${packageId}::coin::create_connector`,
        arguments: [tx.object(currencyHolderId), tx.object(registryId)],
        typeArguments: [],
      });
    } else {
      throw new Error(
        `Unsupported CurrencyHolder object type: ${currencyHolderObjectType}. Expected either template or coin pattern.`,
      );
    }

    return tx;
  }

  /**
   * Check if an error is due to temp_id collision (EUniqueIdRequired)
   */
  static isTempIdCollisionError(error: any): boolean {
    const errorMessage = error?.message || error?.toString() || '';
    // Check for MoveAbort with error code 3 (EUniqueIdRequired)
    // Also check for "Dry run failed" which can contain the MoveAbort error
    return (
      (errorMessage.includes('MoveAbort') && errorMessage.includes(', 3)')) ||
      (errorMessage.includes('Dry run failed') &&
        errorMessage.includes('MoveAbort') &&
        errorMessage.includes('place_dev_order') &&
        errorMessage.includes(', 3)'))
    );
  }

  /**
   * Execute place_dev_order with retry logic for temp_id collisions
   */
  static async executeWithTempIdRetry(
    signAndExecuteTransaction: any,
    createTransactionFn: (tempId: number) => any,
    maxRetries = 3,
    userAddress?: string,
  ): Promise<{ result: any; tempId: number }> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const tempId = this.generateUniqueTempId(userAddress);
      console.log(`🎲 Attempt ${attempt}/${maxRetries} with temp_id:`, tempId);

      try {
        const transaction = createTransactionFn(tempId);
        const result = await signAndExecuteTransaction({ transaction });
        console.log(`✅ Success on attempt ${attempt} with temp_id:`, tempId);
        return { result, tempId };
      } catch (error) {
        console.log(`❌ Attempt ${attempt} failed:`, error);
        lastError = error;

        if (this.isTempIdCollisionError(error)) {
          console.log(
            `🔄 Temp_id collision detected (attempt ${attempt}/${maxRetries}), retrying with new temp_id...`,
          );
          console.log(`🔍 Collision error details:`, error.message);
          // Add small delay before retry
          await new Promise((resolve) => setTimeout(resolve, 100 * attempt));
          continue;
        } else {
          // Non-collision error, don't retry
          console.log('❌ Non-collision error, not retrying');
          throw error;
        }
      }
    }

    console.log(`❌ All ${maxRetries} attempts failed`);
    throw lastError;
  }

  /**
   * Generate a unique temp_id with high entropy to avoid collisions
   * Uses timestamp, random number, and user address for uniqueness
   * CRITICAL: Stays well within JavaScript's Number.MAX_SAFE_INTEGER to avoid precision loss
   * that can cause u64 overflow errors in SUI transactions
   */
  static generateUniqueTempId(userAddress?: string): number {
    // CRITICAL: JavaScript Number.MAX_SAFE_INTEGER is 2^53-1 = 9,007,199,254,740,991
    // We must stay well below this to avoid precision loss that causes u64 overflow
    // Target: Keep temp_id under 10^12 (1 trillion) for safety

    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000); // Reduced to 4 digits

    // Use user address for additional entropy if available
    let userSuffix = 0;
    if (userAddress) {
      try {
        // Take last 4 characters of address and convert to number
        const addressSuffix = userAddress.slice(-4);
        userSuffix = parseInt(addressSuffix, 16) % 10000; // 4 digits max
      } catch (error) {
        console.warn('Failed to parse user address for temp_id:', error);
        userSuffix = Math.floor(Math.random() * 10000);
      }
    } else {
      userSuffix = Math.floor(Math.random() * 10000);
    }

    // MUCH more conservative approach: keep under 10^12
    // Use only the last 8 digits of timestamp to keep numbers small
    const timestampSuffix = timestamp % 100000000; // Last 8 digits (10^8)

    // Ultra-conservative approach to stay well within safe integer bounds
    // Format: [timestamp_5_digits][random_4_digits][user_suffix_2_digits]
    // Max result: 99999 * 1000000 + 9999 * 100 + 99 = ~100 billion (well within safe bounds)
    const ultraSafeTempId =
      (timestampSuffix % 100000) * 1000000 + random * 100 + (userSuffix % 100);

    // Max: 99999 * 1000000 + 9999 * 100 + 99 = ~10^11 (100 billion) - SAFE!

    console.log('🎲 Generated ultra-safe temp_id:', ultraSafeTempId);
    console.log('🔍 Temp_id validation:', {
      value: ultraSafeTempId,
      withinSafeInteger: Number.isSafeInteger(ultraSafeTempId),
      maxSafeInteger: Number.MAX_SAFE_INTEGER,
      ratio: ultraSafeTempId / Number.MAX_SAFE_INTEGER,
    });

    return ultraSafeTempId;
  }

  /**
   * Create a transaction for placing a dev order
   * This registers the temp_id with a DevOrder that accept_connector expects
   */
  static createPlaceDevOrderTransaction(
    coinPackageId: string,
    tempId: number,
    buyAmount: number,
  ): Transaction {
    const tx = new Transaction();
    const config = networkConfigService.getNetworkConfig();

    // MUST call place_dev_order to register the temp_id
    // This creates a DevOrder entry that accept_connector expects
    const suiAmount = Math.max(buyAmount, 0) * 1_000_000_000; // Convert to MIST
    const [sui_coin] = tx.splitCoins(tx.gas, [suiAmount]);

    tx.moveCall({
      target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
      arguments: [
        tx.object(config.contracts.memeConfigId),
        tx.pure.u64(tempId), // Pass the temp_id as the second argument
        sui_coin,
      ],
      typeArguments: [], // place_dev_order doesn't take type arguments
    });

    return tx;
  }

  /**
   * Helper function to determine the correct coin type from CoinMetadata object type
   */
  private static extractCoinTypeFromMetadata(
    coinMetadataObjectType: string,
  ): string {
    // CoinMetadata object type format: 0x2::coin::CoinMetadata<ACTUAL_COIN_TYPE>
    const match = /0x2::coin::CoinMetadata<(.+)>/.exec(coinMetadataObjectType);
    if (match && match[1]) {
      return match[1];
    }
    throw new Error(
      `Could not extract coin type from metadata object type: ${coinMetadataObjectType}`,
    );
  }

  /**
   * Create a transaction for accepting connector and creating bonding curve
   * Note: place_dev_order must be called BEFORE this in a separate transaction
   */
  // eslint-disable-next-line max-params
  static createAcceptConnectorTransaction(
    coinPackageId: string,
    coinSymbol: string,
    sender: string,
    connectorId: string,
    coinMetadataId: string,
    coinMetadataObjectType: string, // Add this parameter to extract the correct coin type
    tempId: number, // Must be provided - no default to avoid collisions
    buyAmount = 0, // Additional buy amount (optional)
  ): Transaction {
    const tx = new Transaction();
    const config = networkConfigService.getNetworkConfig();

    // Extract the correct coin type from the CoinMetadata object type
    const coinType = this.extractCoinTypeFromMetadata(coinMetadataObjectType);

    // Call accept_connector to create the bonding curve
    // Note: accept_connector expects: dex_config, meme_config, connector (as Receiving), metadata
    // It will check that place_dev_order was called previously for this temp_id
    tx.moveCall({
      target: `${config.contracts.hopfunPackageId}::meme::accept_connector`,
      arguments: [
        tx.object(config.contracts.hopdexConfigId), // DexConfig (first parameter)
        tx.object(config.contracts.memeConfigId), // MemeConfig (second parameter)
        tx.object(connectorId), // Connector (third parameter, as Receiving)
        tx.object(coinMetadataId), // CoinMetadata (fourth parameter)
      ],
      typeArguments: [coinType],
    });

    return tx;
  }

  /**
   * Create a transaction for setting up the bonding curve (deprecated - kept for backward compatibility)
   */
  // eslint-disable-next-line max-params
  static createBondingCurveTransaction(
    coinPackageId: string,
    coinSymbol: string,
    sender: string,
    connectorId: string,
    coinMetadataId: string,
    tempId: number, // Must be provided - no default to avoid collisions
    buyAmount = 0, // Initial buy amount in SUI (0 means no initial buy)
  ): Transaction {
    const tx = new Transaction();
    const config = networkConfigService.getNetworkConfig();

    // Generate the module name that was used during publication
    // The module name is fixed as "template" in our bytecode
    const moduleName = 'template';
    // The struct name is fixed as "TEMPLATE" in our bytecode
    const structName = 'TEMPLATE';

    // Create the correct coin type identifier
    const coinType = `${coinPackageId}::${moduleName}::${structName}`;

    // Step 1: Place dev order (pay for bonding curve creation)
    // This creates a DevOrder that will be consumed by accept_connector
    if (buyAmount > 0) {
      const [buyCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(buyAmount)]);
      tx.moveCall({
        target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
        arguments: [
          tx.object(config.contracts.memeConfigId),
          tx.pure.u64(tempId),
          buyCoin,
        ],
      });
    } else {
      // Even with 0 buy amount, we need to call place_dev_order
      const [zeroCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(0)]);
      tx.moveCall({
        target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
        arguments: [
          tx.object(config.contracts.memeConfigId),
          tx.pure.u64(tempId),
          zeroCoin,
        ],
      });
    }

    // Step 2: Accept the connector to create the bonding curve
    // The connector was created when the coin was published and transferred to MEME_SHARED_OBJECT_ID
    // For accept_connector, we can pass the connector directly as an object
    // The SDK will handle the receiving reference automatically when the object is owned by MemeConfig

    tx.moveCall({
      target: `${config.contracts.hopfunPackageId}::meme::accept_connector`,
      arguments: [
        tx.object(config.contracts.hopdexConfigId), // DexConfig
        tx.object(config.contracts.memeConfigId), // MemeConfig
        tx.object(connectorId), // Receiving<Connector<T>> - SDK handles receiving automatically
        tx.object(coinMetadataId), // CoinMetadata<T>
      ],
      typeArguments: [coinType],
    });

    // Set the sender
    tx.setSender(sender);

    return tx;
  }

  /**
   * Execute the complete token creation flow
   * This handles both publishing the coin and creating the bonding curve
   */
  static async executeTokenCreation(
    payload: TokenCreationPayload,
    sender: string,
    signAndExecuteTransaction: (params: {
      transaction: Transaction;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }) => Promise<any>,
  ): Promise<{
    packageId: string;
    bondingCurveId?: string;
    coinMetadataId?: string;
  }> {
    try {
      const config = networkConfigService.getNetworkConfig();

      // Validate network connection and epoch
      await this.validateNetworkConnection();

      // Step 1: Publish the coin module using dynamic compilation
      const publishTx = await this.createPublishCoinTransactionDynamic(
        payload,
        sender,
      );
      const publishResult = await signAndExecuteTransaction({
        transaction: publishTx,
      });

      // The result contains digest and effects (base64 BCS encoded)
      if (!publishResult.digest) {
        throw new TokenCreationError(
          'Transaction failed: no digest returned',
          'TRANSACTION_FAILED',
          publishResult,
        );
      }

      // Fetch the full transaction details to get the created objects
      const transactionDetails = await this.getTransactionDetails(
        publishResult.digest,
      );

      if (
        !transactionDetails?.effects?.created ||
        transactionDetails.effects.created.length === 0
      ) {
        throw new TokenCreationError(
          'Failed to publish coin module: no objects created',
          'TRANSACTION_FAILED',
          transactionDetails,
        );
      }

      // Extract the package ID from objectChanges
      // Published packages have type 'published'
      const publishedPackage = transactionDetails.objectChanges?.find(
        (change: any) => change.type === 'published',
      );

      if (!publishedPackage) {
        // Fallback: try to find in effects.created (old method)
        const packageObject = transactionDetails.effects.created.find(
          (obj: any) => obj.owner === 'Immutable',
        );

        if (!packageObject) {
          throw new TokenCreationError(
            'Could not find published package in transaction result',
            'TRANSACTION_FAILED',
            transactionDetails,
          );
        }

        const packageId = packageObject.reference.objectId;
        console.warn('Using fallback method to extract package ID:', packageId);
      } else {
        console.log('Found published package:', publishedPackage);
      }

      const packageId =
        publishedPackage?.packageId ||
        transactionDetails.effects.created.find(
          (obj: any) => obj.owner === 'Immutable',
        )?.reference?.objectId;

      if (!packageId) {
        console.error('Failed to extract package ID from transaction:', {
          publishedPackage,
          effects: transactionDetails.effects,
          objectChanges: transactionDetails.objectChanges,
        });
        throw new TokenCreationError(
          'Could not extract package ID from publish transaction',
          'TRANSACTION_FAILED',
          transactionDetails,
        );
      }

      console.log('Successfully extracted package ID:', packageId);

      // Extract the CurrencyHolder and CoinMetadata objects from the publish transaction
      // The CurrencyHolder is created by the init function and transferred to the sender
      // The CoinMetadata is frozen (Immutable)

      // Find the CurrencyHolder object that was created and transferred to the sender
      const currencyHolderObject = transactionDetails.objectChanges?.find(
        (change: any) => {
          if (change.type === 'created' && change.objectType) {
            // Check if it's a CurrencyHolder type - it can match either pattern:
            // {coin_package}::template::CurrencyHolder<{coin_package}::template::TEMPLATE> (template)
            // {coin_package}::coin::CurrencyHolder<{coin_package}::coin::COIN> (dynamic compilation)
            const isTemplatePattern =
              change.objectType.includes('::template::CurrencyHolder<') &&
              change.objectType.includes('::template::TEMPLATE>');
            const isDynamicPattern =
              change.objectType.includes('::coin::CurrencyHolder<') &&
              change.objectType.includes('::coin::COIN>');

            if (isTemplatePattern || isDynamicPattern) {
              // Verify the owner is the sender
              const owner = change.owner;
              if (owner && typeof owner === 'object' && owner.AddressOwner) {
                return owner.AddressOwner === sender;
              }
            }
          }
          return false;
        },
      );

      // CoinMetadata has a specific pattern with the coin type and is Immutable
      const coinMetadataObject = transactionDetails.objectChanges?.find(
        (change: any) => {
          if (change.type === 'created' && change.objectType) {
            // Check if it's CoinMetadata and has Immutable owner
            if (change.objectType.includes('0x2::coin::CoinMetadata<')) {
              const owner = change.owner;
              if (owner === 'Immutable') {
                return true;
              }
            }
          }
          return false;
        },
      );

      // Log all created objects for debugging
      console.log(
        'All created objects from publish transaction:',
        transactionDetails.objectChanges
          ?.filter((change: any) => change.type === 'created')
          .map((change: any) => ({
            objectId: change.objectId,
            objectType: change.objectType,
            owner: change.owner,
          })),
      );

      if (!currencyHolderObject || !coinMetadataObject) {
        console.error(
          'Could not find CurrencyHolder or CoinMetadata in transaction result',
          {
            currencyHolderObject,
            coinMetadataObject,
            objectChanges: transactionDetails.objectChanges,
          },
        );
        // Return just the package ID if we can't create the connector
        return { packageId };
      }

      const currencyHolderId = currencyHolderObject.objectId;
      const coinMetadataId = coinMetadataObject.objectId;

      // Validate that the IDs are not undefined and are valid addresses
      if (!currencyHolderId || !coinMetadataId) {
        console.error('Invalid object IDs extracted:', {
          currencyHolderId,
          coinMetadataId,
        });
        return { packageId };
      }

      // Ensure the IDs are in the correct format (should start with 0x)
      if (
        !currencyHolderId.startsWith('0x') ||
        !coinMetadataId.startsWith('0x')
      ) {
        console.error('Object IDs are not in the correct format:', {
          currencyHolderId,
          coinMetadataId,
        });
        return { packageId };
      }

      console.log('Extracted IDs from publish transaction:', {
        packageId,
        currencyHolderId,
        currencyHolderObject,
        coinMetadataId,
        coinMetadataObject,
      });

      // Step 2: Create the connector from the CurrencyHolder
      // This is a two-step process:
      // 2a. Call create_connector to convert CurrencyHolder to Connector
      // 2b. Call accept_connector to create the bonding curve
      let bondingCurveId: string | undefined;

      try {
        console.log('🔗 Starting connector and bonding curve creation...');
        console.log('Package ID:', packageId);
        console.log('Currency Holder ID:', currencyHolderId);
        console.log('Coin Metadata ID:', coinMetadataId);

        // First, create the HopFun connector transaction using template
        const createConnectorTx = this.createConnectorTransaction(
          packageId,
          currencyHolderId,
          currencyHolderObject.objectType, // Pass the object type to determine module name
        );

        console.log('📝 Executing create connector transaction...');
        const createConnectorResult = await signAndExecuteTransaction({
          transaction: createConnectorTx,
        });
        console.log('✅ Create connector result:', createConnectorResult);

        if (!createConnectorResult.digest) {
          throw new TokenCreationError(
            'Create connector transaction failed - no digest returned',
            'TRANSACTION_FAILED',
            createConnectorResult,
          );
        }

        if (createConnectorResult.digest) {
          const connectorDetails = await this.getTransactionDetails(
            createConnectorResult.digest,
          );

          // Find the ConnectorCreated event to get the Connector ID
          // Handle both template (::events::ConnectorCreated) and dynamic (::coin::ConnectorCreated) patterns
          const connectorCreatedEvent = connectorDetails.events?.find(
            (event: any) =>
              event.type.includes('::events::ConnectorCreated') ||
              event.type.includes('::coin::ConnectorCreated'),
          );

          const connectorId = connectorCreatedEvent?.parsedJson?.connector_id;

          if (connectorId) {
            console.log('🎯 Found connector ID:', connectorId);

            // Step 3: Place dev order to register the temp_id
            // This MUST be done in a separate transaction before accept_connector
            console.log(
              '💰 Creating place dev order transaction with retry logic...',
            );

            // Use dynamic temp_id generation with retry logic to avoid collisions
            console.log(
              '💰 Using dynamic temp_id generation with retry logic...',
            );

            const { result: placeOrderResult, tempId: finalTempId } =
              await this.executeWithTempIdRetry(
                signAndExecuteTransaction,
                (tempId: number) =>
                  this.createPlaceDevOrderTransaction(
                    packageId,
                    tempId,
                    0, // Initial buy amount (can be 0)
                  ),
                3, // maxRetries
                sender, // userAddress for entropy
              );

            console.log('✅ Place dev order result:', placeOrderResult);
            console.log('🎯 Final temp_id used:', finalTempId);

            if (!placeOrderResult.digest) {
              throw new TokenCreationError(
                'Place dev order transaction failed - no digest returned',
                'TRANSACTION_FAILED',
                placeOrderResult,
              );
            }

            // Wait for the place_dev_order transaction to be confirmed
            console.log('⏳ Waiting for place dev order confirmation...');
            await this.getTransactionDetails(placeOrderResult.digest);
            console.log('✅ Place dev order confirmed');

            // Step 4: Now accept the connector to create the bonding curve
            console.log('🚀 Creating accept connector transaction...');
            const acceptConnectorTx = this.createAcceptConnectorTransaction(
              packageId,
              payload.symbol,
              sender,
              connectorId,
              coinMetadataId,
              coinMetadataObject.objectType, // Pass the object type to extract coin type
              finalTempId, // Use the same temp_id from place_dev_order
              0, // No additional buy amount (already handled in place_dev_order)
            );

            console.log('📝 Executing accept connector transaction...');
            const acceptResult = await signAndExecuteTransaction({
              transaction: acceptConnectorTx,
            });
            console.log('✅ Accept connector result:', acceptResult);

            if (acceptResult.digest) {
              console.log('📊 Getting accept connector transaction details...');
              const acceptDetails = await this.getTransactionDetails(
                acceptResult.digest,
              );
              console.log(
                '📋 Accept connector transaction details:',
                acceptDetails,
              );

              // Look for the BondingCurve object
              console.log(
                '🔍 Looking for BondingCurve object in object changes...',
              );
              console.log('Object changes:', acceptDetails.objectChanges);

              const bondingCurveObject = acceptDetails.objectChanges?.find(
                (change: any) =>
                  change.type === 'created' &&
                  change.objectType?.includes('::meme::BondingCurve<'),
              );
              console.log('🎯 Found bonding curve object:', bondingCurveObject);
              bondingCurveId = bondingCurveObject?.objectId;

              if (bondingCurveId) {
                console.log(
                  '🎉 Bonding curve created successfully:',
                  bondingCurveId,
                );
              } else {
                console.warn('⚠️ No bonding curve object found in transaction');
              }
            } else {
              throw new TokenCreationError(
                'Accept connector transaction failed - no digest returned',
                'TRANSACTION_FAILED',
                acceptResult,
              );
            }
          } else {
            throw new TokenCreationError(
              'Failed to extract connector ID from transaction - ConnectorCreated event not found',
              'TRANSACTION_FAILED',
              createConnectorResult,
            );
          }
        }
      } catch (error) {
        console.error(
          '❌ CRITICAL: Failed to create connector and bonding curve:',
          error,
        );
        console.error('🔍 Error details:', {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          errorObject: error,
        });

        // Instead of silently failing, let's throw the error to see what's happening
        // This will help us debug the actual issue
        throw new TokenCreationError(
          `Bonding curve creation failed: ${error instanceof Error ? error.message : String(error)}`,
          'TRANSACTION_FAILED',
          error,
        );
      }

      return {
        packageId,
        bondingCurveId,
        coinMetadataId,
      };
    } catch (error) {
      if (error instanceof TokenCreationError) {
        throw error;
      }
      throw new TokenCreationError(
        'Token creation failed',
        'TRANSACTION_FAILED',
        error,
      );
    }
  }

  /**
   * Fetch transaction details from the blockchain with retry mechanism
   */
  private static async getTransactionDetails(digest: string): Promise<any> {
    const config = networkConfigService.getNetworkConfig();
    const maxRetries = 10; // Maximum number of retry attempts
    const initialDelay = 1000; // Initial delay in milliseconds
    const maxDelay = 30000; // Maximum delay between retries

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const response = await fetch(config.rpcUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'sui_getTransactionBlock',
            params: [
              digest,
              {
                showInput: true,
                showRawInput: false,
                showEffects: true,
                showEvents: true,
                showObjectChanges: true,
                showBalanceChanges: true,
              },
            ],
          }),
        });

        const data = await response.json();

        // Check if we got a valid result
        if (data.result) {
          console.log(
            `Transaction ${digest} fetched successfully after ${attempt + 1} attempt(s)`,
          );
          return data.result;
        }

        // Check for specific error conditions
        if (data.error) {
          const errorMessage = data.error.message || '';

          // If the error indicates the transaction doesn't exist yet, retry
          if (
            errorMessage.includes(
              'Could not find the referenced transaction',
            ) ||
            errorMessage.includes('Transaction not found') ||
            errorMessage.includes('not found')
          ) {
            console.log(
              `Transaction ${digest} not found yet, attempt ${attempt + 1}/${maxRetries}`,
            );

            // If this isn't the last attempt, wait before retrying
            if (attempt < maxRetries - 1) {
              // Calculate delay with exponential backoff and jitter
              const baseDelay = Math.min(
                initialDelay * Math.pow(2, attempt),
                maxDelay,
              );
              const jitter = Math.random() * 0.3 * baseDelay; // Add up to 30% jitter
              const delay = baseDelay + jitter;

              console.log(`Waiting ${Math.round(delay)}ms before retry...`);
              await new Promise((resolve) => setTimeout(resolve, delay));
              continue; // Try again
            }
          }

          // For other errors, throw immediately
          throw new Error(
            errorMessage || 'Failed to fetch transaction details',
          );
        }

        // If we got here, something unexpected happened
        throw new Error('Unexpected response format from RPC');
      } catch (error) {
        // If this is a network error and not the last attempt, retry
        if (attempt < maxRetries - 1 && error instanceof TypeError) {
          const delay = Math.min(initialDelay * Math.pow(2, attempt), maxDelay);
          console.log(`Network error, retrying in ${delay}ms...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }

        // If this is the last attempt or a non-retryable error, throw
        if (attempt === maxRetries - 1) {
          console.error(
            `Failed to fetch transaction ${digest} after ${maxRetries} attempts:`,
            error,
          );
          throw new TokenCreationError(
            `Transaction ${digest} not found after ${maxRetries} attempts. The transaction may still be processing.`,
            'TRANSACTION_NOT_FOUND',
            error,
          );
        }

        // For other errors, throw immediately
        console.error('Error fetching transaction details:', error);
        throw new TokenCreationError(
          'Failed to fetch transaction details',
          'NETWORK_ERROR',
          error,
        );
      }
    }

    // This should never be reached, but just in case
    throw new TokenCreationError(
      'Failed to fetch transaction details',
      'NETWORK_ERROR',
    );
  }

  /**
   * Helper to validate the token creation payload
   */
  static validatePayload(payload: TokenCreationPayload): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Name validation
    if (!payload.name || payload.name.length === 0) {
      errors.push('Token name is required');
    } else if (payload.name.length > 32) {
      errors.push('Token name must be 32 characters or less');
    }

    // Symbol validation
    if (!payload.symbol || payload.symbol.length === 0) {
      errors.push('Token symbol is required');
    } else if (payload.symbol.length > 10) {
      errors.push('Token symbol must be 10 characters or less');
    } else if (!/^[A-Z0-9]+$/i.test(payload.symbol)) {
      errors.push('Token symbol must contain only letters and numbers');
    }

    // Description validation
    if (payload.description && payload.description.length > 500) {
      errors.push('Description must be 500 characters or less');
    }

    // URL validations
    if (payload.twitter && !this.isValidUrl(payload.twitter)) {
      errors.push('Invalid Twitter URL');
    }

    if (payload.website && !this.isValidUrl(payload.website)) {
      errors.push('Invalid website URL');
    }

    if (payload.telegram && !this.isValidUrl(payload.telegram)) {
      errors.push('Invalid Telegram URL');
    }

    // Total supply validation
    if (!payload.totalSupply || payload.totalSupply <= 0) {
      errors.push('Total supply must be greater than 0');
    } else if (payload.totalSupply > 1e15) {
      // Max 1 quadrillion
      errors.push('Total supply is too large');
    }

    // Decimals validation
    if (payload.decimals < 0 || payload.decimals > 18) {
      errors.push('Decimals must be between 0 and 18');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Validate network connection and epoch to prevent ZKLogin signature issues
   */
  private static async validateNetworkConnection(): Promise<void> {
    try {
      const client = this.initializeClient();
      const config = networkConfigService.getNetworkConfig();

      // Get current epoch from the network
      const checkpoints = await client.getCheckpoints({
        limit: 1,
        descendingOrder: true,
      });
      const latestCheckpoint = checkpoints.data[0];
      const currentEpoch = latestCheckpoint?.epoch;

      console.log('Network validation:', {
        network: networkConfigService.getCurrentNetwork(),
        rpcUrl: config.rpcUrl,
        currentEpoch,
        checkpoint: latestCheckpoint.sequenceNumber,
      });

      // Log epoch information for debugging ZKLogin issues
      if (currentEpoch) {
        console.log(`Current network epoch: ${currentEpoch}`);
        console.log(
          `Max acceptable ZKLogin epoch would be: ${Number(currentEpoch) + 47}`,
        );
      }
    } catch (error) {
      console.warn('Network validation failed:', error);
      throw new TokenCreationError(
        'Network connection validation failed. Please check your network connection and try again.',
        'NETWORK_ERROR',
        error,
      );
    }
  }

  /**
   * Check if the current network is supported for token creation
   */
  static isNetworkSupported(): boolean {
    const config = networkConfigService.getNetworkConfig();

    // Check if the necessary contract addresses are configured
    return (
      config.contracts.hopfunPackageId !== '0x0' &&
      config.contracts.memeConfigId !== '0x0'
    );
  }

  /**
   * Get a user-friendly error message for common errors
   */
  static getErrorMessage(error: any): string {
    if (error instanceof TokenCreationError) {
      return error.message;
    }

    // Import network mismatch detection
    const {
      detectNetworkMismatch,
      getNetworkMismatchMessage,
    } = require('@/utils/wallet-reset');

    // Check for potential network mismatch first
    if (detectNetworkMismatch(error)) {
      return getNetworkMismatchMessage();
    }

    // Check for ZKLogin signature errors
    if (
      error?.message?.includes('ZKLogin max epoch too large') ||
      error?.details?.message?.includes('ZKLogin max epoch too large')
    ) {
      return 'ZKLogin signature error: Please disconnect and reconnect your wallet to the correct network (devnet), then try again. This usually happens when the wallet has cached an outdated signature or is connected to a different network.';
    }

    if (
      error?.message?.includes('Invalid user signature') ||
      error?.details?.message?.includes('Invalid user signature')
    ) {
      return 'Invalid signature: Please disconnect and reconnect your wallet to the correct network (devnet), then try again.';
    }

    if (error?.message?.includes('Rejected from user')) {
      return 'Transaction was cancelled by user';
    }

    if (error?.message?.includes('Insufficient balance')) {
      return 'Insufficient balance to pay for gas';
    }

    if (error?.message?.includes('Network')) {
      return 'Network error. Please check your connection and try again';
    }

    // Check for temp_id collision errors
    if (this.isTempIdCollisionError(error)) {
      return 'Token creation temporarily unavailable due to high demand. Please try again in a few seconds.';
    }

    return error?.message || 'An unexpected error occurred';
  }
}
