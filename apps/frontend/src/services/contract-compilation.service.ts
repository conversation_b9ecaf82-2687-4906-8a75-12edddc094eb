import type { TokenCreationPayload } from './token-creation.service';

// API Response types
export interface CompileContractResponse {
  status: 'success' | 'error';
  message?: string;
  data?: {
    bytecode: string; // Base64 encoded bytecode
    dependencies: string[]; // Package dependencies
    sourceCode: string; // Generated Move source code
    moduleName: string; // Generated module name
    packageId?: string; // If deployed
    metadata: ContractMetadata;
    compilationId: string; // Unique ID for this compilation
  };
  error?: {
    code: string;
    details?: unknown;
  };
}

export interface GenerateSourceResponse {
  status: 'success' | 'error';
  message?: string;
  data?: {
    sourceCode: string;
    moduleName: string;
    metadata: ContractMetadata;
  };
  error?: {
    code: string;
    details?: unknown;
  };
}

export interface CompilerStatusResponse {
  status: 'success' | 'error';
  message?: string;
  data?: {
    available: boolean;
    version: string;
    status: 'ready' | 'unavailable';
  };
  error?: {
    code: string;
    details?: unknown;
  };
}

// Request types
export interface ContractMetadata {
  name: string;
  symbol: string;
  description?: string;
  imageUrl?: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  totalSupply: number;
  decimals: number;
}

export interface CompileContractRequest {
  metadata: ContractMetadata;
  network: 'MAINNET' | 'TESTNET' | 'DEVNET';
  templateVersion?: string;
}

// Error types
export class ContractCompilationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: unknown,
  ) {
    super(message);
    this.name = 'ContractCompilationError';
  }
}

export class ContractCompilationService {
  private static readonly API_BASE_URL =
    process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  private static readonly COMPILATION_ENDPOINT = '/api/contract-compilation';

  /**
   * Converts TokenCreationPayload to ContractMetadata
   */
  static tokenPayloadToMetadata(
    payload: TokenCreationPayload,
  ): ContractMetadata {
    return {
      name: payload.name,
      symbol: payload.symbol,
      description: payload.description,
      imageUrl: payload.imageUrl,
      twitter: payload.twitter,
      website: payload.website,
      telegram: payload.telegram,
      totalSupply: payload.totalSupply,
      decimals: payload.decimals,
    };
  }

  /**
   * Generates Move source code from metadata
   */
  static async generateSource(
    metadata: ContractMetadata,
  ): Promise<GenerateSourceResponse> {
    try {
      console.log('🔧 Generating Move source code...', {
        symbol: metadata.symbol,
      });

      const response = await fetch(
        `${this.API_BASE_URL}${this.COMPILATION_ENDPOINT}/generate`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(metadata),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new ContractCompilationError(
          data.error?.message || 'Failed to generate source code',
          data.error?.code || 'GENERATION_FAILED',
          data.error?.details,
        );
      }

      console.log('✅ Move source code generated successfully', {
        moduleName: data.data.moduleName,
        sourceCodeLength: data.data.sourceCode.length,
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to generate source code:', error);

      if (error instanceof ContractCompilationError) {
        throw error;
      }

      throw new ContractCompilationError(
        'Network error occurred while generating source code',
        'NETWORK_ERROR',
        {
          originalError: error instanceof Error ? error.message : String(error),
        },
      );
    }
  }

  /**
   * Compiles Move contract to bytecode
   */
  static async compileContract(
    request: CompileContractRequest,
  ): Promise<CompileContractResponse> {
    try {
      console.log('🔨 Compiling Move contract...', {
        symbol: request.metadata.symbol,
        network: request.network,
      });

      const response = await fetch(
        `${this.API_BASE_URL}${this.COMPILATION_ENDPOINT}/compile`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new ContractCompilationError(
          data.error?.message || 'Failed to compile contract',
          data.error?.code || 'COMPILATION_FAILED',
          data.error?.details,
        );
      }

      console.log('✅ Move contract compiled successfully', {
        compilationId: data.data.compilationId,
        bytecodeSize: data.data.bytecode.length,
        dependencies: data.data.dependencies.length,
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to compile contract:', error);

      if (error instanceof ContractCompilationError) {
        throw error;
      }

      throw new ContractCompilationError(
        'Network error occurred while compiling contract',
        'NETWORK_ERROR',
        {
          originalError: error instanceof Error ? error.message : String(error),
        },
      );
    }
  }

  /**
   * Gets compiler status
   */
  static async getCompilerStatus(): Promise<CompilerStatusResponse> {
    try {
      const response = await fetch(
        `${this.API_BASE_URL}${this.COMPILATION_ENDPOINT}/compiler/status`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new ContractCompilationError(
          data.error?.message || 'Failed to get compiler status',
          data.error?.code || 'STATUS_CHECK_FAILED',
          data.error?.details,
        );
      }

      return data;
    } catch (error) {
      console.error('❌ Failed to get compiler status:', error);

      if (error instanceof ContractCompilationError) {
        throw error;
      }

      throw new ContractCompilationError(
        'Network error occurred while checking compiler status',
        'NETWORK_ERROR',
        {
          originalError: error instanceof Error ? error.message : String(error),
        },
      );
    }
  }

  /**
   * Compiles a token contract from TokenCreationPayload
   * This is the main method that should be used by the token creation flow
   */
  static async compileTokenContract(
    payload: TokenCreationPayload,
    network: 'MAINNET' | 'TESTNET' | 'DEVNET' = 'DEVNET',
  ): Promise<{
    bytecode: string;
    dependencies: string[];
    sourceCode: string;
    moduleName: string;
    compilationId: string;
  }> {
    try {
      // Convert payload to metadata
      const metadata = this.tokenPayloadToMetadata(payload);

      // Compile the contract
      const result = await this.compileContract({
        metadata,
        network,
        templateVersion: 'latest',
      });

      if (result.status !== 'success' || !result.data) {
        throw new ContractCompilationError(
          result.message || 'Compilation failed',
          result.error?.code || 'COMPILATION_FAILED',
          result.error?.details,
        );
      }

      return {
        bytecode: result.data.bytecode,
        dependencies: result.data.dependencies,
        sourceCode: result.data.sourceCode,
        moduleName: result.data.moduleName,
        compilationId: result.data.compilationId,
      };
    } catch (error) {
      console.error('❌ Token contract compilation failed:', error);
      throw error;
    }
  }

  /**
   * Validates that the compiler is available before attempting compilation
   */
  static async validateCompilerAvailability(): Promise<boolean> {
    try {
      const status = await this.getCompilerStatus();
      return status.status === 'success' && status.data?.available === true;
    } catch (error) {
      console.warn('⚠️ Could not check compiler availability:', error);
      return false;
    }
  }
}
