#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to investigate the type mismatch issue
 * The error suggests that the Connector object type doesn't match what the new contract expects
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    // OLD CONTRACT ADDRESSES (where Connector objects were created)
    oldHopfunPackageId: '0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c',
    oldMemeConfigId: '0xe4341287bd48105e879dfba7bc3151b5bd881efb23c6b5dbe0efefa896b0d7aa',
    
    // NEW FIXED CONTRACT ADDRESSES (where accept_connector is called)
    hopfunPackageId: '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
    memeConfigId: '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
    
    // UNCHANGED ADDRESSES
    hopdexConfigId: '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
    registryId: '0x51fdbf98bb1b5e9a574ce2c380cd0d59301db9e75484a17387d15bb5ef5d3d7d',
  },
};

async function investigateTypeMismatch() {
  console.log('🔍 Investigating Type Mismatch Issue\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  console.log('📋 Contract Addresses:');
  console.log(`  OLD HopFun Package: ${config.oldHopfunPackageId}`);
  console.log(`  NEW HopFun Package: ${config.hopfunPackageId}`);
  console.log(`  OLD MemeConfig: ${config.oldMemeConfigId}`);
  console.log(`  NEW MemeConfig: ${config.memeConfigId}\n`);

  // Step 1: Check if there are any Connector objects from the old contract
  console.log('🔍 Step 1: Looking for Connector objects from OLD contract...');
  try {
    const oldMemeConfigObjects = await client.getOwnedObjects({
      owner: config.oldMemeConfigId,
      options: {
        showType: true,
        showContent: true
      }
    });
    
    console.log(`📦 Found ${oldMemeConfigObjects.data.length} objects owned by OLD MemeConfig`);
    
    const oldConnectorObjects = oldMemeConfigObjects.data.filter(obj => 
      obj.data?.type?.includes('::connector::Connector')
    );
    
    console.log(`🔗 Found ${oldConnectorObjects.length} Connector objects from OLD contract`);
    
    if (oldConnectorObjects.length > 0) {
      console.log('✅ OLD Connector objects found:');
      oldConnectorObjects.forEach((obj, index) => {
        console.log(`   ${index + 1}. Object ID: ${obj.data.objectId}`);
        console.log(`      Type: ${obj.data.type}`);
        console.log(`      Package: ${obj.data.type.split('::')[0]}`);
      });
    } else {
      console.log('❌ No OLD Connector objects found');
    }
    
  } catch (error) {
    console.log('❌ Error searching for OLD Connector objects:', error.message);
  }

  // Step 2: Check the type signature difference
  console.log('\n🔍 Step 2: Comparing Connector types between contracts...');
  
  console.log('📝 Expected types:');
  console.log(`   OLD Connector type: ${config.oldHopfunPackageId}::connector::Connector<T>`);
  console.log(`   NEW Connector type: ${config.hopfunPackageId}::connector::Connector<T>`);
  console.log('');
  console.log('🚨 TYPE MISMATCH IDENTIFIED:');
  console.log('   - Connector objects were created with the OLD contract');
  console.log('   - accept_connector function expects NEW contract Connector type');
  console.log('   - These are incompatible types in Sui Move');

  // Step 3: Check the new accept_connector function signature
  console.log('\n🔍 Step 3: Checking NEW accept_connector function signature...');
  try {
    const packageModules = await client.getNormalizedMoveModulesByPackage({
      package: config.hopfunPackageId
    });
    
    if (packageModules.meme && packageModules.meme.exposedFunctions.accept_connector) {
      const acceptConnectorFunction = packageModules.meme.exposedFunctions.accept_connector;
      console.log('✅ NEW accept_connector function found');
      
      // Check the third parameter (Connector<T>)
      const connectorParam = acceptConnectorFunction.parameters[2];
      console.log('📝 Third parameter (Connector) details:');
      console.log(`   Expected type: ${JSON.stringify(connectorParam, null, 2)}`);
      
      if (connectorParam.Struct && connectorParam.Struct.address === config.hopfunPackageId) {
        console.log('✅ Function expects Connector from NEW contract package');
      } else {
        console.log('❌ Unexpected Connector type in function signature');
      }
    } else {
      console.log('❌ accept_connector function not found in NEW contract');
    }
  } catch (error) {
    console.log('❌ Error checking NEW function signature:', error.message);
  }

  // Step 4: Explain the solution
  console.log('\n💡 SOLUTION ANALYSIS:');
  console.log('');
  console.log('🚨 ROOT CAUSE:');
  console.log('   - Connector objects exist from OLD contract (0xa531...)');
  console.log('   - NEW accept_connector expects Connector from NEW contract (0x0701...)');
  console.log('   - These are different types and cannot be used interchangeably');
  console.log('');
  console.log('✅ SOLUTION OPTIONS:');
  console.log('   1. 🔄 RECOMMENDED: Update registry to point to NEW MemeConfig');
  console.log('      - This ensures new tokens use the NEW contract');
  console.log('      - Existing tokens will need to be recreated');
  console.log('');
  console.log('   2. 🔧 ALTERNATIVE: Create migration function');
  console.log('      - Convert OLD Connector to NEW Connector format');
  console.log('      - More complex but preserves existing tokens');
  console.log('');
  console.log('🎯 IMMEDIATE ACTION NEEDED:');
  console.log('   - Update the registry to use the NEW MemeConfig address');
  console.log('   - This will ensure new token creations use the fixed contract');
  console.log('   - Test with a fresh token creation (not existing Connector objects)');
}

async function main() {
  try {
    await investigateTypeMismatch();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
