#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to check the exact accept_connector function signature
 * This will help us understand what type it expects
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    hopfunPackageId: '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
    memeConfigId: '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
  },
};

async function checkAcceptConnectorSignature() {
  console.log('🔍 Checking accept_connector Function Signature\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  console.log('📋 Configuration:');
  console.log(`  HopFun Package: ${config.hopfunPackageId}`);
  console.log(`  MemeConfig: ${config.memeConfigId}\n`);

  try {
    console.log('🔍 Fetching HopFun package modules...');
    const packageModules = await client.getNormalizedMoveModulesByPackage({
      package: config.hopfunPackageId
    });
    
    if (packageModules.meme && packageModules.meme.exposedFunctions.accept_connector) {
      const acceptConnectorFunction = packageModules.meme.exposedFunctions.accept_connector;
      console.log('✅ accept_connector function found');
      console.log('');
      console.log('📝 COMPLETE FUNCTION SIGNATURE:');
      console.log(JSON.stringify(acceptConnectorFunction, null, 2));
      console.log('');
      
      console.log('🎯 PARAMETER ANALYSIS:');
      acceptConnectorFunction.parameters.forEach((param, index) => {
        console.log(`Parameter ${index}:`);
        console.log(`  Type: ${JSON.stringify(param, null, 2)}`);
        
        if (param.Struct && param.Struct.name === 'Connector') {
          console.log(`  🔗 CONNECTOR PARAMETER FOUND!`);
          console.log(`     Expected Package: ${param.Struct.address}`);
          console.log(`     Expected Module: ${param.Struct.module}`);
          console.log(`     Expected Struct: ${param.Struct.name}`);
          console.log(`     Type Arguments: ${JSON.stringify(param.Struct.typeArguments)}`);
        }
        
        if (param.Struct && param.Struct.name === 'Receiving') {
          console.log(`  📨 RECEIVING PARAMETER FOUND!`);
          console.log(`     Receiving Type: ${JSON.stringify(param.Struct.typeArguments)}`);
          if (param.Struct.typeArguments && param.Struct.typeArguments[0] && param.Struct.typeArguments[0].Struct) {
            const innerType = param.Struct.typeArguments[0].Struct;
            console.log(`     Inner Package: ${innerType.address}`);
            console.log(`     Inner Module: ${innerType.module}`);
            console.log(`     Inner Struct: ${innerType.name}`);
          }
        }
        console.log('');
      });
      
      // Check what type of Connector is expected
      const connectorParam = acceptConnectorFunction.parameters.find(param => 
        (param.Struct && param.Struct.name === 'Connector') ||
        (param.Struct && param.Struct.name === 'Receiving' && 
         param.Struct.typeArguments && param.Struct.typeArguments[0] && 
         param.Struct.typeArguments[0].Struct && param.Struct.typeArguments[0].Struct.name === 'Connector')
      );
      
      if (connectorParam) {
        console.log('🚨 CONNECTOR TYPE ANALYSIS:');
        
        if (connectorParam.Struct.name === 'Connector') {
          console.log('   Function expects: DIRECT Connector<T>');
          console.log(`   Expected package: ${connectorParam.Struct.address}`);
          console.log(`   Expected module: ${connectorParam.Struct.module}`);
        } else if (connectorParam.Struct.name === 'Receiving') {
          const innerConnector = connectorParam.Struct.typeArguments[0].Struct;
          console.log('   Function expects: Receiving<Connector<T>>');
          console.log(`   Expected package: ${innerConnector.address}`);
          console.log(`   Expected module: ${innerConnector.module}`);
        }
        
        console.log('');
        console.log('🔍 COMPARISON WITH DYNAMIC COIN:');
        console.log('   Dynamic coin creates: {dynamic_package}::coin::Connector<T>');
        console.log(`   HopFun expects: ${connectorParam.Struct.address || connectorParam.Struct.typeArguments[0].Struct.address}::${connectorParam.Struct.module || connectorParam.Struct.typeArguments[0].Struct.module}::Connector<T>`);
        
        const expectedPackage = connectorParam.Struct.address || connectorParam.Struct.typeArguments[0].Struct.address;
        const expectedModule = connectorParam.Struct.module || connectorParam.Struct.typeArguments[0].Struct.module;
        
        if (expectedPackage === config.hopfunPackageId && expectedModule === 'connector') {
          console.log('   ❌ TYPE MISMATCH: HopFun expects its own Connector, not dynamic coin Connector');
          console.log('');
          console.log('💡 SOLUTION NEEDED:');
          console.log('   1. Create bridge function in HopFun to accept dynamic Connectors');
          console.log('   2. Or modify accept_connector to accept any Connector with matching fields');
          console.log('   3. Or create conversion function from dynamic Connector to HopFun Connector');
        } else {
          console.log('   ✅ TYPE MATCH: Function accepts any Connector type');
        }
      }
      
    } else {
      console.log('❌ accept_connector function not found');
    }
    
  } catch (error) {
    console.log('❌ Error checking function signature:', error.message);
  }
  
  console.log('');
  console.log('📊 SUMMARY:');
  console.log('   This analysis will help determine the exact type mismatch issue');
  console.log('   and guide us to the correct solution for the fourth transaction.');
}

async function main() {
  try {
    await checkAcceptConnectorSignature();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
