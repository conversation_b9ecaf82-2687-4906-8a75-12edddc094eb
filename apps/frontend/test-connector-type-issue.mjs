#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to investigate the exact connector type issue
 * This will help us understand what type of Connector is being created
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    hopfunPackageId: '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
    memeConfigId: '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
  },
};

async function investigateConnectorTypes() {
  console.log('🔍 Investigating Connector Type Issue\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  console.log('📋 Configuration:');
  console.log(`  HopFun Package: ${config.hopfunPackageId}`);
  console.log(`  MemeConfig: ${config.memeConfigId}\n`);

  // Step 1: Check what accept_connector expects
  console.log('🔍 Step 1: Checking accept_connector function signature...');
  try {
    const packageModules = await client.getNormalizedMoveModulesByPackage({
      package: config.hopfunPackageId
    });
    
    if (packageModules.meme && packageModules.meme.exposedFunctions.accept_connector) {
      const acceptConnectorFunction = packageModules.meme.exposedFunctions.accept_connector;
      console.log('✅ accept_connector function found');
      console.log('📝 Parameters:');
      acceptConnectorFunction.parameters.forEach((param, index) => {
        console.log(`   ${index}: ${JSON.stringify(param, null, 2)}`);
      });
      
      // Focus on parameter 2 (the Connector)
      const connectorParam = acceptConnectorFunction.parameters[2];
      console.log('\n🎯 Connector parameter (index 2):');
      console.log(`   Expected type: ${JSON.stringify(connectorParam, null, 2)}`);
      
      if (connectorParam.Struct) {
        console.log(`   Expected package: ${connectorParam.Struct.address}`);
        console.log(`   Expected module: ${connectorParam.Struct.module}`);
        console.log(`   Expected struct: ${connectorParam.Struct.name}`);
      }
    } else {
      console.log('❌ accept_connector function not found');
    }
  } catch (error) {
    console.log('❌ Error checking function signature:', error.message);
  }

  // Step 2: Check what types of Connector objects exist
  console.log('\n🔍 Step 2: Checking existing Connector objects...');
  try {
    // Check MemeConfig for Connector objects
    const memeConfigObjects = await client.getOwnedObjects({
      owner: config.memeConfigId,
      options: {
        showType: true,
        showContent: true
      }
    });
    
    console.log(`📦 Found ${memeConfigObjects.data.length} objects owned by MemeConfig`);
    
    const connectorObjects = memeConfigObjects.data.filter(obj => 
      obj.data?.type?.includes('Connector')
    );
    
    console.log(`🔗 Found ${connectorObjects.length} Connector-like objects:`);
    
    connectorObjects.forEach((obj, index) => {
      console.log(`   ${index + 1}. Object ID: ${obj.data.objectId}`);
      console.log(`      Type: ${obj.data.type}`);
      
      // Parse the type to understand the structure
      const typeMatch = obj.data.type.match(/^(.+)::(.+)::(.+)<(.+)>$/);
      if (typeMatch) {
        const [, packageId, module, struct, typeArg] = typeMatch;
        console.log(`      Package: ${packageId}`);
        console.log(`      Module: ${module}`);
        console.log(`      Struct: ${struct}`);
        console.log(`      Type Arg: ${typeArg}`);
        
        // Check if this matches what accept_connector expects
        if (packageId === config.hopfunPackageId && module === 'connector' && struct === 'Connector') {
          console.log(`      ✅ This is a REAL HopFun Connector`);
        } else if (struct === 'PlaceholderConnector') {
          console.log(`      ❌ This is a PlaceholderConnector (not compatible)`);
        } else {
          console.log(`      ⚠️  This is an unknown Connector type`);
        }
      }
      
      if (obj.data.content) {
        console.log(`      Content: ${JSON.stringify(obj.data.content, null, 2)}`);
      }
    });
    
  } catch (error) {
    console.log('❌ Error checking Connector objects:', error.message);
  }

  // Step 3: Analyze the issue and solution
  console.log('\n📊 CONNECTOR TYPE ANALYSIS:');
  console.log('');
  console.log('🚨 THE ISSUE:');
  console.log('   1. accept_connector expects: hopfun::connector::Connector<T>');
  console.log('   2. Dynamic coins create: {coin_package}::coin::PlaceholderConnector<T>');
  console.log('   3. These are completely different types');
  console.log('   4. Sui Move type system prevents using one for the other');
  console.log('');
  console.log('💡 SOLUTION OPTIONS:');
  console.log('');
  console.log('   A. 🔧 MODIFY DYNAMIC COMPILATION (RECOMMENDED):');
  console.log('      - Change dynamic coin template to create real Connectors');
  console.log('      - Import HopFun contract in dynamic compilation');
  console.log('      - Call hopfun::connector::new_from_supply instead of creating PlaceholderConnector');
  console.log('');
  console.log('   B. 🌉 CREATE BRIDGE FUNCTION:');
  console.log('      - Add function to HopFun contract: convert_placeholder_connector');
  console.log('      - Takes PlaceholderConnector and creates real Connector');
  console.log('      - Requires contract upgrade');
  console.log('');
  console.log('   C. 🔄 CHANGE ACCEPT_CONNECTOR:');
  console.log('      - Modify accept_connector to accept PlaceholderConnector');
  console.log('      - Extract supply and create real Connector internally');
  console.log('      - Requires contract upgrade');
  console.log('');
  console.log('   D. 📦 USE TEMPLATE COINS ONLY:');
  console.log('      - Disable dynamic compilation');
  console.log('      - Use only template-based coins (which create real Connectors)');
  console.log('      - Simpler but less flexible');
  console.log('');
  console.log('🎯 IMMEDIATE ACTION:');
  console.log('   - Option A is best: Fix dynamic compilation to create real Connectors');
  console.log('   - This requires updating the contract template service');
  console.log('   - Import HopFun contract and call connector::new_from_supply');
  console.log('   - This will make dynamic coins compatible with accept_connector');
}

async function main() {
  try {
    await investigateConnectorTypes();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
