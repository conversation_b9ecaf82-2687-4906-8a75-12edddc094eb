#!/usr/bin/env node

/**
 * Test script to verify the HopFun bridge compilation works
 * This tests that the create_hopfun_connector function compiles correctly
 */

async function testHopFunBridgeCompilation() {
  console.log('🧪 Testing HopFun Bridge Compilation\n');
  
  const API_BASE_URL = 'http://localhost:3001'; // Adjust if needed
  const COMPILE_ENDPOINT = `${API_BASE_URL}/contract-compilation/compile`;
  
  console.log('📋 TEST CONFIGURATION:');
  console.log(`  API Base URL: ${API_BASE_URL}`);
  console.log(`  Compile Endpoint: ${COMPILE_ENDPOINT}\n`);
  
  // Test payload
  const testPayload = {
    metadata: {
      name: "HopFun Bridge Test",
      symbol: "BRIDGE",
      description: "Testing HopFun bridge compilation",
      imageUrl: "https://example.com/bridge.png",
      totalSupply: "1000000000",
      decimals: 9,
      twitter: "https://twitter.com/bridge",
      website: "https://bridge.com",
      telegram: "https://t.me/bridge"
    },
    network: "DEVNET",
    templateVersion: "latest"
  };
  
  console.log('📝 TEST PAYLOAD:');
  console.log(JSON.stringify(testPayload, null, 2));
  console.log('');
  
  try {
    console.log('🚀 Sending compilation request...');
    
    const response = await fetch(COMPILE_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
    
    const responseData = await response.json();
    
    if (response.ok) {
      console.log('✅ COMPILATION SUCCESS!');
      console.log('');
      console.log('📦 Response Data:');
      console.log(`  Success: ${responseData.success}`);
      console.log(`  Message: ${responseData.message}`);
      
      if (responseData.data) {
        console.log('');
        console.log('🔍 Compilation Results:');
        console.log(`  Module Name: ${responseData.data.moduleName}`);
        console.log(`  Compilation ID: ${responseData.data.compilationId}`);
        console.log(`  Bytecode Size: ${responseData.data.bytecode ? responseData.data.bytecode.length : 'N/A'} characters`);
        console.log(`  Source Code Size: ${responseData.data.sourceCode ? responseData.data.sourceCode.length : 'N/A'} characters`);
        
        if (responseData.data.sourceCode) {
          console.log('');
          console.log('🔍 CHECKING FOR HOPFUN BRIDGE FUNCTION:');
          const sourceCode = responseData.data.sourceCode;
          
          // Check for the bridge function
          if (sourceCode.includes('create_hopfun_connector')) {
            console.log('   ✅ create_hopfun_connector function found');
          } else {
            console.log('   ❌ create_hopfun_connector function NOT found');
          }
          
          // Check for HopFun import
          if (sourceCode.includes('0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb::connector::new_from_supply')) {
            console.log('   ✅ HopFun connector::new_from_supply call found');
          } else {
            console.log('   ❌ HopFun connector::new_from_supply call NOT found');
          }
          
          // Check for registry parameter
          if (sourceCode.includes('0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e::registry::ConfigRegistry')) {
            console.log('   ✅ Registry ConfigRegistry import found');
          } else {
            console.log('   ❌ Registry ConfigRegistry import NOT found');
          }
          
          console.log('');
          console.log('📝 Bridge Function Preview (first 1000 chars):');
          const bridgeFunctionStart = sourceCode.indexOf('create_hopfun_connector');
          if (bridgeFunctionStart !== -1) {
            const preview = sourceCode.substring(bridgeFunctionStart, bridgeFunctionStart + 1000);
            console.log(preview + '...');
          } else {
            console.log('   Bridge function not found in source code');
          }
        }
        
        if (responseData.data.bytecode) {
          console.log('');
          console.log('💾 Bytecode Generated:');
          console.log(`  Base64 Length: ${responseData.data.bytecode.length}`);
          console.log(`  First 100 chars: ${responseData.data.bytecode.substring(0, 100)}...`);
        }
      }
      
      console.log('');
      console.log('🎉 HOPFUN BRIDGE COMPILATION TEST PASSED!');
      console.log('   ✅ No compilation errors');
      console.log('   ✅ Bytecode successfully generated');
      console.log('   ✅ Bridge function included in compilation');
      console.log('   ✅ Ready for HopFun Connector creation');
      
    } else {
      console.log('❌ COMPILATION FAILED!');
      console.log('');
      console.log('📊 Error Response:');
      console.log(JSON.stringify(responseData, null, 2));
      
      if (responseData.error) {
        console.log('');
        console.log('🚨 Error Details:');
        console.log(`  Code: ${responseData.error.code}`);
        console.log(`  Message: ${responseData.error.message}`);
        
        if (responseData.error.details) {
          console.log('  Details:', JSON.stringify(responseData.error.details, null, 2));
        }
        
        // Check for specific errors
        if (responseData.error.message.includes('Unbound module')) {
          console.log('');
          console.log('🔍 ANALYSIS: Unbound module error detected');
          console.log('   This might be due to missing HopFun contract import');
          console.log('   Check if the HopFun package address is correct');
        }
        
        if (responseData.error.message.includes('Dependencies on Bridge, MoveStdlib, Sui')) {
          console.log('');
          console.log('🔍 ANALYSIS: Dependency injection error detected');
          console.log('   This is the old error that should be fixed now');
        }
      }
    }
    
  } catch (error) {
    console.log('❌ REQUEST FAILED!');
    console.log('');
    console.log('🚨 Error Details:');
    console.log(`  Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('🔍 ANALYSIS: Server connection refused');
      console.log('   Make sure the server is running on the correct port');
      console.log('   Try: npm run dev (in the server directory)');
    }
  }
  
  console.log('');
  console.log('📋 NEXT STEPS:');
  console.log('');
  console.log('If compilation succeeded:');
  console.log('  ✅ The HopFun bridge function is working correctly');
  console.log('  ✅ Dynamic compilation can create real HopFun Connectors');
  console.log('  ✅ Ready to test complete token creation flow');
  console.log('');
  console.log('If compilation failed:');
  console.log('  🔄 Check HopFun contract address in bridge function');
  console.log('  🔍 Verify registry contract address is correct');
  console.log('  🛠️  Check server logs for detailed error information');
  console.log('');
  console.log('🚀 EXPECTED FLOW:');
  console.log('  1. ✅ Compile with bridge function');
  console.log('  2. ✅ Publish dynamic coin');
  console.log('  3. ✅ Call create_hopfun_connector (creates real HopFun Connector)');
  console.log('  4. ✅ Place dev order');
  console.log('  5. ✅ Accept connector (no type mismatch!)');
}

async function main() {
  try {
    await testHopFunBridgeCompilation();
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

main();
