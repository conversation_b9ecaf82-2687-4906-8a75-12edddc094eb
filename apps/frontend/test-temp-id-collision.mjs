#!/usr/bin/env node

import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';

/**
 * Test script to investigate the temp_id collision issue
 * Error code 3 (EUniqueIdRequired) suggests the temp_id is already in use
 */

const networkConfig = {
  network: 'devnet',
  rpcUrl: getFullnodeUrl('devnet'),
  contracts: {
    hopfunPackageId: '0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb',
    memeConfigId: '0xe36b3ba834ff7e8e5234e9c153304322e024da4df8948c6995da1542c1e56ed4',
    hopdexConfigId: '0x1db08fa9bd3ae76519b66ed35aa03eb83049ba5db8a69a80d8635ca68c77e8bd',
    registryId: '0x6a8daf347e2322c1ce60f262ec408d5d89574cedd29eb6ae09777dc133d1d8c5',
  },
};

async function investigateTempIdCollision() {
  console.log('🔍 Investigating temp_id Collision Issue\n');
  
  const client = new SuiClient({ url: networkConfig.rpcUrl });
  const config = networkConfig.contracts;
  
  console.log('📋 Configuration:');
  console.log(`  HopFun Package: ${config.hopfunPackageId}`);
  console.log(`  MemeConfig: ${config.memeConfigId}\n`);

  // Step 1: Check MemeConfig dynamic fields
  console.log('🔍 Step 1: Checking MemeConfig dynamic fields...');
  try {
    const memeConfigObject = await client.getObject({
      id: config.memeConfigId,
      options: {
        showContent: true,
        showType: true
      }
    });
    
    if (memeConfigObject.data) {
      console.log('✅ MemeConfig found');
      console.log(`   Type: ${memeConfigObject.data.type}`);
      console.log(`   Version: ${memeConfigObject.data.version}`);
      
      // Check if there are dynamic fields
      const dynamicFields = await client.getDynamicFields({
        parentId: config.memeConfigId
      });
      
      console.log(`📦 Found ${dynamicFields.data.length} dynamic fields in MemeConfig`);
      
      if (dynamicFields.data.length > 0) {
        console.log('🔍 Dynamic fields found:');
        for (const field of dynamicFields.data) {
          console.log(`   - Name: ${JSON.stringify(field.name)}`);
          console.log(`     Type: ${field.objectType}`);
          console.log(`     Object ID: ${field.objectId}`);
          
          // If it's a u64 field (temp_id), get its details
          if (field.name.type === 'u64') {
            try {
              const fieldObject = await client.getObject({
                id: field.objectId,
                options: { showContent: true }
              });
              
              if (fieldObject.data && fieldObject.data.content) {
                console.log(`     Content: ${JSON.stringify(fieldObject.data.content, null, 2)}`);
              }
            } catch (error) {
              console.log(`     Error getting field content: ${error.message}`);
            }
          }
        }
        
        console.log('🚨 ISSUE IDENTIFIED:');
        console.log('   - MemeConfig has existing dynamic fields (temp_ids)');
        console.log('   - place_dev_order requires unique temp_id');
        console.log('   - Frontend may be reusing temp_ids from previous attempts');
        
      } else {
        console.log('✅ No dynamic fields found - temp_id collision not from existing orders');
      }
    } else {
      console.log('❌ MemeConfig not found');
    }
  } catch (error) {
    console.log('❌ Error checking MemeConfig:', error.message);
  }

  // Step 2: Analyze the temp_id generation logic
  console.log('\n🔍 Step 2: Analyzing temp_id generation...');
  console.log('📝 From the error, the issue is:');
  console.log('   - place_dev_order checks: assert!(!df::exists_<u64>(config.id(), temp_id), EUniqueIdRequired)');
  console.log('   - This means temp_id already exists in MemeConfig dynamic fields');
  console.log('   - Error code 3 = EUniqueIdRequired');
  console.log('');
  console.log('🔍 Possible causes:');
  console.log('   1. Frontend generates non-unique temp_ids');
  console.log('   2. Previous failed transactions left temp_ids in MemeConfig');
  console.log('   3. Multiple users using same temp_id generation logic');
  console.log('   4. Temp_id not properly cleaned up after successful token creation');

  // Step 3: Check temp_id generation in frontend
  console.log('\n🔍 Step 3: Temp_id generation analysis...');
  console.log('💡 SOLUTION APPROACHES:');
  console.log('');
  console.log('1. 🎲 IMPROVE TEMP_ID GENERATION:');
  console.log('   - Use more entropy (timestamp + random + user address)');
  console.log('   - Use crypto.getRandomValues() for better randomness');
  console.log('   - Include user address in temp_id calculation');
  console.log('');
  console.log('2. 🔄 RETRY WITH NEW TEMP_ID:');
  console.log('   - If place_dev_order fails with EUniqueIdRequired');
  console.log('   - Generate new temp_id and retry');
  console.log('   - Implement exponential backoff');
  console.log('');
  console.log('3. 🧹 CLEANUP MECHANISM:');
  console.log('   - Add function to clean up unused temp_ids');
  console.log('   - Set expiration time for temp_ids');
  console.log('   - Allow admin to clean up stale entries');
  console.log('');
  console.log('4. 🔍 QUERY BEFORE USE:');
  console.log('   - Check if temp_id exists before using it');
  console.log('   - Generate new temp_id if collision detected');
  console.log('   - Use deterministic but unique generation');

  // Step 4: Recommend immediate fix
  console.log('\n🎯 IMMEDIATE FIX RECOMMENDATION:');
  console.log('');
  console.log('✅ FRONTEND SOLUTION:');
  console.log('   1. Improve temp_id generation with more entropy');
  console.log('   2. Add retry logic for EUniqueIdRequired error');
  console.log('   3. Include user address and timestamp in temp_id');
  console.log('');
  console.log('🔧 IMPLEMENTATION:');
  console.log('   - Use: Date.now() + Math.random() + userAddress.slice(-8)');
  console.log('   - Convert to u64 safely (ensure < 2^64)');
  console.log('   - Add retry mechanism in token creation service');
  console.log('');
  console.log('📝 EXAMPLE TEMP_ID GENERATION:');
  console.log('   const timestamp = Date.now();');
  console.log('   const random = Math.floor(Math.random() * 1000000);');
  console.log('   const userSuffix = parseInt(userAddress.slice(-8), 16) % 1000000;');
  console.log('   const tempId = (timestamp % 1000000000) * 1000000 + random + userSuffix;');
}

async function main() {
  try {
    await investigateTempIdCollision();
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main();
