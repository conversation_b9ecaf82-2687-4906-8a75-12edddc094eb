{
  "status": "error",
  "message": "An error occurred, please try again later",
  "error": {
    "code": "SYNTAX_ERROR",
    "message": "Contract compilation failed: Compilation failed: Build failed: Command failed: sui move build --path /Users/<USER>/Programming/hopaggregator/hopfun/apps/server/temp/compilations/6fcdff12690991f012812b98f924979e\n[note] Dependencies on Bridge, MoveStdlib, Sui, and SuiSystem are automatically added, but this feature is disabled for your package because you have explicitly included dependencies on Sui. Consider removing these dependencies from Move.toml.\nFETCHING GIT DEPENDENCY https://github.com/MystenLabs/sui.git\nCloning into '/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__testnet'...\nINCLUDING DEPENDENCY Sui\nINCLUDING DEPENDENCY MoveStdlib\nBUILDING DynamicCoin\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:5:9\n  [0m[34m│[0m\n[0m[34m5[0m [0m[34m│[0m     use [0m[31mhopfun::connector[0m;\n  [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^[0m [0m[31mInvalid 'use'. Unbound module: 'hopfun::connector'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:6:9\n  [0m[34m│[0m\n[0m[34m6[0m [0m[34m│[0m     use [0m[31mconfig_registry::registry[0m::ConfigRegistry;\n  [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mInvalid 'use'. Unbound module: 'config_registry::registry'[0m\n\n[0m[1m[38;5;9merror[E01003][0m[1m: invalid modifier[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:9:5\n  [0m[34m│[0m\n[0m[34m9[0m [0m[34m│[0m     [0m[31mpublic[0m struct TEST1 has drop {}\n  [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mInvalid struct declaration. Structs cannot have visibility modifiers as they are always 'public'[0m\n  [0m[34m│[0m\n  [0m[34m=[0m Starting in the Move 2024 edition visibility must be annotated on struct declarations.\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:9:5\n  [0m[34m│[0m\n[0m[34m9[0m [0m[34m│[0m     [0m[31mpublic[0m struct TEST1 has drop {}\n  [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mStruct visibility modifiers are not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n  [0m[34m│[0m\n  [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:11:11\n   [0m[34m│[0m\n[0m[34m11[0m [0m[34m│[0m     const [0m[33mICON_URL[0m: vector<u8> = b\"https://i.ibb.co/Tx2vG8c7/df7b8dba74f1.jpg\";\n   [0m[34m│[0m           [0m[33m^^^^^^^^[0m [0m[33mThe constant 'ICON_URL' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:14:11\n   [0m[34m│[0m\n[0m[34m14[0m [0m[34m│[0m     const [0m[33mTWITTER[0m: vector<u8> = b\"https://twitter.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^[0m [0m[33mThe constant 'TWITTER' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:15:11\n   [0m[34m│[0m\n[0m[34m15[0m [0m[34m│[0m     const [0m[33mWEBSITE[0m: vector<u8> = b\"https://website.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^[0m [0m[33mThe constant 'WEBSITE' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:16:11\n   [0m[34m│[0m\n[0m[34m16[0m [0m[34m│[0m     const [0m[33mTELEGRAM[0m: vector<u8> = b\"https://telegram.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^^[0m [0m[33mThe constant 'TELEGRAM' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:24:11\n   [0m[34m│[0m\n[0m[34m24[0m [0m[34m│[0m     const [0m[33mTOTAL_SUPPLY[0m: u64 = 690000000;\n   [0m[34m│[0m           [0m[33m^^^^^^^^^^^^[0m [0m[33mThe constant 'TOTAL_SUPPLY' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E01003][0m[1m: invalid modifier[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:27:5\n   [0m[34m│[0m\n[0m[34m27[0m [0m[34m│[0m     [0m[31mpublic[0m struct CurrencyHolder<phantom T> has key {\n   [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mInvalid struct declaration. Structs cannot have visibility modifiers as they are always 'public'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m Starting in the Move 2024 edition visibility must be annotated on struct declarations.\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:27:5\n   [0m[34m│[0m\n[0m[34m27[0m [0m[34m│[0m     [0m[31mpublic[0m struct CurrencyHolder<phantom T> has key {\n   [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mStruct visibility modifiers are not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;9merror[Sui E02003][0m[1m: invalid 'init' function[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:36:9\n   [0m[34m│[0m\n[0m[34m36[0m [0m[34m│[0m     fun [0m[31minit[0m(witness: TEST1, ctx: &mut sui::tx_context::TxContext) {\n   [0m[34m│[0m         [0m[31m^^^^[0m          [0m[34m-----[0m [0m[34mInvalid parameter 'witness' of type 'coin_template::test1_b6ee720e::TEST1'. Expected a one-time witness type, 'coin_template::test1_b6ee720e::TEST1_B6EE720E[0m\n   [0m[34m│[0m         [0m[31m│[0m              \n   [0m[34m│[0m         [0m[31mInvalid 'init' function declaration[0m\n   [0m[34m│[0m\n   [0m[34m=[0m One-time witness types are structs with the following requirements: their name is the upper-case version of the module's name, they have no fields (or a single boolean field), they have no type parameters, and they have only the 'drop' ability.\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:38:14\n   [0m[34m│[0m\n[0m[34m38[0m [0m[34m│[0m         let ([0m[33mmut[0m treasury_cap, metadata) = coin::create_currency<TEST1>(\n   [0m[34m│[0m              [0m[33m^^^[0m [0m[33mUnused local variable 'mut'. Consider removing or prefixing with an underscore: '_mut'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E01002][0m[1m: unexpected token[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:38:18\n   [0m[34m│[0m\n[0m[34m38[0m [0m[34m│[0m         let (mut [0m[31mtreasury_cap[0m, metadata) = coin::create_currency<TEST1>(\n   [0m[34m│[0m                  [0m[31m^^^^^^^^^^^^[0m\n   [0m[34m│[0m                  [0m[31m│[0m\n   [0m[34m│[0m                  [0m[31mUnexpected 'treasury_cap'[0m\n   [0m[34m│[0m                  [0m[34mExpected ',' or ')'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:44:13\n   [0m[34m│[0m\n[0m[34m44[0m [0m[34m│[0m             [0m[31moption[0m::some(sui::url::new_unsafe_from_bytes(ICON_URL)),\n   [0m[34m│[0m             [0m[31m^^^^^^[0m [0m[31mUnbound module alias 'option'[0m\n\n[0m[1m[38;5;9merror[E03009][0m[1m: unbound variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:52:22\n   [0m[34m│[0m\n[0m[34m52[0m [0m[34m│[0m         let supply = [0m[31mtreasury_cap[0m.mint(TOTAL_SUPPLY, ctx);\n   [0m[34m│[0m                      [0m[31m^^^^^^^^^^^^[0m [0m[31mUnbound variable 'treasury_cap'[0m\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:52:22\n   [0m[34m│[0m\n[0m[34m52[0m [0m[34m│[0m         let supply = [0m[31mtreasury_cap.mint(TOTAL_SUPPLY, ctx)[0m;\n   [0m[34m│[0m                      [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mMethod syntax is not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;9merror[E03009][0m[1m: unbound variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:58:13\n   [0m[34m│[0m\n[0m[34m58[0m [0m[34m│[0m             [0m[31mtreasury_cap[0m,\n   [0m[34m│[0m             [0m[31m^^^^^^^^^^^^[0m [0m[31mUnbound variable 'treasury_cap'[0m\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:59:21\n   [0m[34m│[0m\n[0m[34m59[0m [0m[34m│[0m             supply: [0m[31msupply.into_balance()[0m,\n   [0m[34m│[0m                     [0m[31m^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mMethod syntax is not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:70:9\n   [0m[34m│[0m\n[0m[34m70[0m [0m[34m│[0m         [0m[33mregistry[0m: &ConfigRegistry,\n   [0m[34m│[0m         [0m[33m^^^^^^^^[0m [0m[33mUnused parameter 'registry'. Consider removing or prefixing with an underscore: '_registry'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03004][0m[1m: unbound type[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:70:20\n   [0m[34m│[0m\n[0m[34m70[0m [0m[34m│[0m         registry: &[0m[31mConfigRegistry[0m,\n   [0m[34m│[0m                    [0m[31m^^^^^^^^^^^^^^[0m [0m[31mUnbound type 'ConfigRegistry' in current scope[0m\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:71:9\n   [0m[34m│[0m\n[0m[34m71[0m [0m[34m│[0m         [0m[33mctx[0m: &mut sui::tx_context::TxContext\n   [0m[34m│[0m         [0m[33m^^^[0m [0m[33mUnused parameter 'ctx'. Consider removing or prefixing with an underscore: '_ctx'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:76:13\n   [0m[34m│[0m\n[0m[34m76[0m [0m[34m│[0m             [0m[33msupply[0m,\n   [0m[34m│[0m             [0m[33m^^^^^^[0m [0m[33mUnused local variable 'supply'. Consider removing or prefixing with an underscore: '_supply'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:77:13\n   [0m[34m│[0m\n[0m[34m77[0m [0m[34m│[0m             [0m[33mtemp_id[0m,\n   [0m[34m│[0m             [0m[33m^^^^^^^[0m [0m[33mUnused local variable 'temp_id'. Consider removing or prefixing with an underscore: '_temp_id'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:78:13\n   [0m[34m│[0m\n[0m[34m78[0m [0m[34m│[0m             [0m[33mcreator[0m\n   [0m[34m│[0m             [0m[33m^^^^^^^[0m [0m[33mUnused local variable 'creator'. Consider removing or prefixing with an underscore: '_creator'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:86:9\n   [0m[34m│[0m\n[0m[34m86[0m [0m[34m│[0m         [0m[31mconnector[0m::new_from_supply<TEST1>(\n   [0m[34m│[0m         [0m[31m^^^^^^^^^[0m [0m[31mUnbound module alias 'connector'[0m\n\n",
    "details": {
      "compilationId": "9e5a2f6f93ed09efd32c760ed1c0035b",
      "originalError": "Compilation failed: Build failed: Command failed: sui move build --path /Users/<USER>/Programming/hopaggregator/hopfun/apps/server/temp/compilations/6fcdff12690991f012812b98f924979e\n[note] Dependencies on Bridge, MoveStdlib, Sui, and SuiSystem are automatically added, but this feature is disabled for your package because you have explicitly included dependencies on Sui. Consider removing these dependencies from Move.toml.\nFETCHING GIT DEPENDENCY https://github.com/MystenLabs/sui.git\nCloning into '/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__testnet'...\nINCLUDING DEPENDENCY Sui\nINCLUDING DEPENDENCY MoveStdlib\nBUILDING DynamicCoin\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:5:9\n  [0m[34m│[0m\n[0m[34m5[0m [0m[34m│[0m     use [0m[31mhopfun::connector[0m;\n  [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^[0m [0m[31mInvalid 'use'. Unbound module: 'hopfun::connector'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:6:9\n  [0m[34m│[0m\n[0m[34m6[0m [0m[34m│[0m     use [0m[31mconfig_registry::registry[0m::ConfigRegistry;\n  [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mInvalid 'use'. Unbound module: 'config_registry::registry'[0m\n\n[0m[1m[38;5;9merror[E01003][0m[1m: invalid modifier[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:9:5\n  [0m[34m│[0m\n[0m[34m9[0m [0m[34m│[0m     [0m[31mpublic[0m struct TEST1 has drop {}\n  [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mInvalid struct declaration. Structs cannot have visibility modifiers as they are always 'public'[0m\n  [0m[34m│[0m\n  [0m[34m=[0m Starting in the Move 2024 edition visibility must be annotated on struct declarations.\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n  [0m[34m┌─[0m ./sources/test1_b6ee720e.move:9:5\n  [0m[34m│[0m\n[0m[34m9[0m [0m[34m│[0m     [0m[31mpublic[0m struct TEST1 has drop {}\n  [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mStruct visibility modifiers are not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n  [0m[34m│[0m\n  [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:11:11\n   [0m[34m│[0m\n[0m[34m11[0m [0m[34m│[0m     const [0m[33mICON_URL[0m: vector<u8> = b\"https://i.ibb.co/Tx2vG8c7/df7b8dba74f1.jpg\";\n   [0m[34m│[0m           [0m[33m^^^^^^^^[0m [0m[33mThe constant 'ICON_URL' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:14:11\n   [0m[34m│[0m\n[0m[34m14[0m [0m[34m│[0m     const [0m[33mTWITTER[0m: vector<u8> = b\"https://twitter.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^[0m [0m[33mThe constant 'TWITTER' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:15:11\n   [0m[34m│[0m\n[0m[34m15[0m [0m[34m│[0m     const [0m[33mWEBSITE[0m: vector<u8> = b\"https://website.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^[0m [0m[33mThe constant 'WEBSITE' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:16:11\n   [0m[34m│[0m\n[0m[34m16[0m [0m[34m│[0m     const [0m[33mTELEGRAM[0m: vector<u8> = b\"https://telegram.com\";\n   [0m[34m│[0m           [0m[33m^^^^^^^^[0m [0m[33mThe constant 'TELEGRAM' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09011][0m[1m: unused constant[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:24:11\n   [0m[34m│[0m\n[0m[34m24[0m [0m[34m│[0m     const [0m[33mTOTAL_SUPPLY[0m: u64 = 690000000;\n   [0m[34m│[0m           [0m[33m^^^^^^^^^^^^[0m [0m[33mThe constant 'TOTAL_SUPPLY' is never used. Consider removing it.[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E01003][0m[1m: invalid modifier[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:27:5\n   [0m[34m│[0m\n[0m[34m27[0m [0m[34m│[0m     [0m[31mpublic[0m struct CurrencyHolder<phantom T> has key {\n   [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mInvalid struct declaration. Structs cannot have visibility modifiers as they are always 'public'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m Starting in the Move 2024 edition visibility must be annotated on struct declarations.\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:27:5\n   [0m[34m│[0m\n[0m[34m27[0m [0m[34m│[0m     [0m[31mpublic[0m struct CurrencyHolder<phantom T> has key {\n   [0m[34m│[0m     [0m[31m^^^^^^[0m [0m[31mStruct visibility modifiers are not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;9merror[Sui E02003][0m[1m: invalid 'init' function[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:36:9\n   [0m[34m│[0m\n[0m[34m36[0m [0m[34m│[0m     fun [0m[31minit[0m(witness: TEST1, ctx: &mut sui::tx_context::TxContext) {\n   [0m[34m│[0m         [0m[31m^^^^[0m          [0m[34m-----[0m [0m[34mInvalid parameter 'witness' of type 'coin_template::test1_b6ee720e::TEST1'. Expected a one-time witness type, 'coin_template::test1_b6ee720e::TEST1_B6EE720E[0m\n   [0m[34m│[0m         [0m[31m│[0m              \n   [0m[34m│[0m         [0m[31mInvalid 'init' function declaration[0m\n   [0m[34m│[0m\n   [0m[34m=[0m One-time witness types are structs with the following requirements: their name is the upper-case version of the module's name, they have no fields (or a single boolean field), they have no type parameters, and they have only the 'drop' ability.\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:38:14\n   [0m[34m│[0m\n[0m[34m38[0m [0m[34m│[0m         let ([0m[33mmut[0m treasury_cap, metadata) = coin::create_currency<TEST1>(\n   [0m[34m│[0m              [0m[33m^^^[0m [0m[33mUnused local variable 'mut'. Consider removing or prefixing with an underscore: '_mut'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E01002][0m[1m: unexpected token[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:38:18\n   [0m[34m│[0m\n[0m[34m38[0m [0m[34m│[0m         let (mut [0m[31mtreasury_cap[0m, metadata) = coin::create_currency<TEST1>(\n   [0m[34m│[0m                  [0m[31m^^^^^^^^^^^^[0m\n   [0m[34m│[0m                  [0m[31m│[0m\n   [0m[34m│[0m                  [0m[31mUnexpected 'treasury_cap'[0m\n   [0m[34m│[0m                  [0m[34mExpected ',' or ')'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:44:13\n   [0m[34m│[0m\n[0m[34m44[0m [0m[34m│[0m             [0m[31moption[0m::some(sui::url::new_unsafe_from_bytes(ICON_URL)),\n   [0m[34m│[0m             [0m[31m^^^^^^[0m [0m[31mUnbound module alias 'option'[0m\n\n[0m[1m[38;5;9merror[E03009][0m[1m: unbound variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:52:22\n   [0m[34m│[0m\n[0m[34m52[0m [0m[34m│[0m         let supply = [0m[31mtreasury_cap[0m.mint(TOTAL_SUPPLY, ctx);\n   [0m[34m│[0m                      [0m[31m^^^^^^^^^^^^[0m [0m[31mUnbound variable 'treasury_cap'[0m\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:52:22\n   [0m[34m│[0m\n[0m[34m52[0m [0m[34m│[0m         let supply = [0m[31mtreasury_cap.mint(TOTAL_SUPPLY, ctx)[0m;\n   [0m[34m│[0m                      [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mMethod syntax is not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;9merror[E03009][0m[1m: unbound variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:58:13\n   [0m[34m│[0m\n[0m[34m58[0m [0m[34m│[0m             [0m[31mtreasury_cap[0m,\n   [0m[34m│[0m             [0m[31m^^^^^^^^^^^^[0m [0m[31mUnbound variable 'treasury_cap'[0m\n\n[0m[1m[38;5;9merror[E13001][0m[1m: feature is not supported in specified edition[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:59:21\n   [0m[34m│[0m\n[0m[34m59[0m [0m[34m│[0m             supply: [0m[31msupply.into_balance()[0m,\n   [0m[34m│[0m                     [0m[31m^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mMethod syntax is not supported by current edition 'legacy'; the '2024' edition supports this feature[0m\n   [0m[34m│[0m\n   [0m[34m=[0m You can update the edition in the 'Move.toml', or via command line flag if invoking the compiler directly.\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:70:9\n   [0m[34m│[0m\n[0m[34m70[0m [0m[34m│[0m         [0m[33mregistry[0m: &ConfigRegistry,\n   [0m[34m│[0m         [0m[33m^^^^^^^^[0m [0m[33mUnused parameter 'registry'. Consider removing or prefixing with an underscore: '_registry'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03004][0m[1m: unbound type[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:70:20\n   [0m[34m│[0m\n[0m[34m70[0m [0m[34m│[0m         registry: &[0m[31mConfigRegistry[0m,\n   [0m[34m│[0m                    [0m[31m^^^^^^^^^^^^^^[0m [0m[31mUnbound type 'ConfigRegistry' in current scope[0m\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:71:9\n   [0m[34m│[0m\n[0m[34m71[0m [0m[34m│[0m         [0m[33mctx[0m: &mut sui::tx_context::TxContext\n   [0m[34m│[0m         [0m[33m^^^[0m [0m[33mUnused parameter 'ctx'. Consider removing or prefixing with an underscore: '_ctx'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:76:13\n   [0m[34m│[0m\n[0m[34m76[0m [0m[34m│[0m             [0m[33msupply[0m,\n   [0m[34m│[0m             [0m[33m^^^^^^[0m [0m[33mUnused local variable 'supply'. Consider removing or prefixing with an underscore: '_supply'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:77:13\n   [0m[34m│[0m\n[0m[34m77[0m [0m[34m│[0m             [0m[33mtemp_id[0m,\n   [0m[34m│[0m             [0m[33m^^^^^^^[0m [0m[33mUnused local variable 'temp_id'. Consider removing or prefixing with an underscore: '_temp_id'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09002][0m[1m: unused variable[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:78:13\n   [0m[34m│[0m\n[0m[34m78[0m [0m[34m│[0m             [0m[33mcreator[0m\n   [0m[34m│[0m             [0m[33m^^^^^^^[0m [0m[33mUnused local variable 'creator'. Consider removing or prefixing with an underscore: '_creator'[0m\n   [0m[34m│[0m\n   [0m[34m=[0m This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n   [0m[34m┌─[0m ./sources/test1_b6ee720e.move:86:9\n   [0m[34m│[0m\n[0m[34m86[0m [0m[34m│[0m         [0m[31mconnector[0m::new_from_supply<TEST1>(\n   [0m[34m│[0m         [0m[31m^^^^^^^^^[0m [0m[31mUnbound module alias 'connector'[0m\n\n",
      "duration": 20967
    }
  }
}
