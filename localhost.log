sharebx.js:8 1
sharebx.js:20 1
css.js:38 cssjs
css.js:51 enabled.
css.js:89 go
css.js:336 cssjs
network-diagnostic.tsx:25 Checking network info...
network-diagnostic.tsx:29 Network config: {network: 'devnet', rpcUrl: 'https://fullnode.devnet.sui.io:443', contracts: {…}}
report-hmr-latency.js:14 [Fast Refresh] done in 1755612980855ms
hot-reloader-app.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 375ms
image:1  GET http://localhost:3000/_next/image?url=https%3A%2F%2Fi.ibb.co%2FPv5G5mtd%2F80b37c27f1bb.jpg&w=256&q=75 500 (Internal Server Error)
hook.js:608 Accordion is changing from uncontrolled to controlled. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.
overrideMethod @ hook.js:608
eval @ index.mjs:34
react_stack_bottom_frame @ react-dom-client.development.js:23638
runWithFiberInDEV @ react-dom-client.development.js:873
commitHookEffectListMount @ react-dom-client.development.js:12296
commitHookPassiveMountEffects @ react-dom-client.development.js:12417
commitPassiveMountOnFiber @ react-dom-client.development.js:14338
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14341
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14341
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14341
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14341
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14341
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14331
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
commitPassiveMountOnFiber @ react-dom-client.development.js:14465
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14311
launch-token-dialog.tsx:201 Creating token with data: {imageUrl: 'https://i.ibb.co/Pv5G5mtd/80b37c27f1bb.jpg', name: 'Test 2', symbol: 'TEST2', description: 'Test 2 description', website: 'https://website.com', …}
token-creation.service.ts:1101 Network validation: {network: 'devnet', rpcUrl: 'https://fullnode.devnet.sui.io:443', currentEpoch: '21', checkpoint: '371200'}
token-creation.service.ts:1110 Current network epoch: 21
token-creation.service.ts:1111 Max acceptable ZKLogin epoch would be: 68
token-creation.service.ts:98 🔨 Using dynamic compilation...
contract-compilation.service.ts:167 🔨 Compiling Move contract... {symbol: 'TEST2', network: 'DEVNET'}
contract-compilation.service.ts:193 ✅ Move contract compiled successfully {compilationId: '32a1a2b1d16e9863b27e200361d2800f', bytecodeSize: 2072, dependencies: 2}
token-creation.service.ts:107 ✅ Dynamic compilation successful: {moduleName: 'test2_adbb8d2f', compilationId: '32a1a2b1d16e9863b27e200361d2800f', bytecodeLength: 2072}
react-dom-client.development.js:16795 [Violation] 'setTimeout' handler took 57ms
token-creation.service.ts:929 Transaction 5EbiGPJoVnTWhqU9d3z8jMJpAV5X2twn7Dn51HDnu8DL fetched successfully after 1 attempt(s)
token-creation.service.ts:572 Found published package: {type: 'published', packageId: '0xebd633d5c3431d455a32dab7516fc32791fd46b883ffbd9b343f0992d7ca559e', version: '1', digest: 't69ymro9GK2CkxPYHAtqcoPHC4RbMpJtWC9YFuqD95f', modules: Array(1)}
token-creation.service.ts:594 Successfully extracted package ID: 0xebd633d5c3431d455a32dab7516fc32791fd46b883ffbd9b343f0992d7ca559e
token-creation.service.ts:643 All created objects from publish transaction: (3) [{…}, {…}, {…}]
token-creation.service.ts:691 Extracted IDs from publish transaction: {packageId: '0xebd633d5c3431d455a32dab7516fc32791fd46b883ffbd9b343f0992d7ca559e', currencyHolderId: '0x82f56154e86b561550f32092a7c06d3897277023efe760fcd3af91ca6a434a5e', currencyHolderObject: {…}, coinMetadataId: '0xfad96f88e397e25ed3ff48f6a644ad3f54165bd83f1cdddf8f40153b508bed4e', coinMetadataObject: {…}}
token-creation.service.ts:706 🔗 Starting connector and bonding curve creation...
token-creation.service.ts:707 Package ID: 0xebd633d5c3431d455a32dab7516fc32791fd46b883ffbd9b343f0992d7ca559e
token-creation.service.ts:708 Currency Holder ID: 0x82f56154e86b561550f32092a7c06d3897277023efe760fcd3af91ca6a434a5e
token-creation.service.ts:709 Coin Metadata ID: 0xfad96f88e397e25ed3ff48f6a644ad3f54165bd83f1cdddf8f40153b508bed4e
token-creation.service.ts:212 Creating HopFun connector using template: {packageId: '0xebd633d5c3431d455a32dab7516fc32791fd46b883ffbd9b343f0992d7ca559e', currencyHolderId: '0x82f56154e86b561550f32092a7c06d3897277023efe760fcd3af91ca6a434a5e', registryId: '0x6a8daf347e2322c1ce60f262ec408d5d89574cedd29eb6ae09777dc133d1d8c5'}
token-creation.service.ts:857 ❌ CRITICAL: Failed to create connector and bonding curve: Error: Dynamic coin compilation is currently disabled due to import issues. Using template-based compilation.
    at TokenCreationService.createConnectorTransaction (token-creation.service.ts:236:13)
    at TokenCreationService.executeTokenCreation (token-creation.service.ts:712:40)
    at async onSubmit (launch-token-dialog.tsx:268:22)
    at async eval (index.esm.mjs:2079:17)
overrideMethod @ hook.js:608
error @ intercept-console-error.js:57
executeTokenCreation @ token-creation.service.ts:857
await in executeTokenCreation
onSubmit @ launch-token-dialog.tsx:268
eval @ index.esm.mjs:2079
await in eval
executeDispatch @ react-dom-client.development.js:16922
runWithFiberInDEV @ react-dom-client.development.js:873
processDispatchQueue @ react-dom-client.development.js:16972
eval @ react-dom-client.development.js:17573
batchedUpdates$1 @ react-dom-client.development.js:3313
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17126
dispatchEvent @ react-dom-client.development.js:21309
dispatchDiscreteEvent @ react-dom-client.development.js:21277
token-creation.service.ts:861 🔍 Error details: {message: 'Dynamic coin compilation is currently disabled due… import issues. Using template-based compilation.', stack: 'Error: Dynamic coin compilation is currently disab…dules/react-hook-form/dist/index.esm.mjs:2079:17)', errorObject: Error: Dynamic coin compilation is currently disabled due to import issues. Using template-based co…}
overrideMethod @ hook.js:608
error @ intercept-console-error.js:57
executeTokenCreation @ token-creation.service.ts:861
await in executeTokenCreation
onSubmit @ launch-token-dialog.tsx:268
eval @ index.esm.mjs:2079
await in eval
executeDispatch @ react-dom-client.development.js:16922
runWithFiberInDEV @ react-dom-client.development.js:873
processDispatchQueue @ react-dom-client.development.js:16972
eval @ react-dom-client.development.js:17573
batchedUpdates$1 @ react-dom-client.development.js:3313
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17126
dispatchEvent @ react-dom-client.development.js:21309
dispatchDiscreteEvent @ react-dom-client.development.js:21277
launch-token-dialog.tsx:303 Token creation error: TokenCreationError: Bonding curve creation failed: Dynamic coin compilation is currently disabled due to import issues. Using template-based compilation.
    at TokenCreationService.executeTokenCreation (token-creation.service.ts:869:15)
    at async onSubmit (launch-token-dialog.tsx:268:22)
    at async eval (index.esm.mjs:2079:17)
overrideMethod @ hook.js:608
error @ intercept-console-error.js:57
onSubmit @ launch-token-dialog.tsx:303
await in onSubmit
eval @ index.esm.mjs:2079
await in eval
executeDispatch @ react-dom-client.development.js:16922
runWithFiberInDEV @ react-dom-client.development.js:873
processDispatchQueue @ react-dom-client.development.js:16972
eval @ react-dom-client.development.js:17573
batchedUpdates$1 @ react-dom-client.development.js:3313
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17126
dispatchEvent @ react-dom-client.development.js:21309
dispatchDiscreteEvent @ react-dom-client.development.js:21277
launch-token-dialog.tsx:336 Token creation failed: {code: 'TRANSACTION_FAILED', details: Error: Dynamic coin compilation is currently disabled due to import issues. Using template-based co…}
overrideMethod @ hook.js:608
error @ intercept-console-error.js:57
onSubmit @ launch-token-dialog.tsx:336
await in onSubmit
eval @ index.esm.mjs:2079
await in eval
executeDispatch @ react-dom-client.development.js:16922
runWithFiberInDEV @ react-dom-client.development.js:873
processDispatchQueue @ react-dom-client.development.js:16972
eval @ react-dom-client.development.js:17573
batchedUpdates$1 @ react-dom-client.development.js:3313
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17126
dispatchEvent @ react-dom-client.development.js:21309
dispatchDiscreteEvent @ react-dom-client.development.js:21277
index.mjs:1100 [Violation] 'setTimeout' handler took 59ms
