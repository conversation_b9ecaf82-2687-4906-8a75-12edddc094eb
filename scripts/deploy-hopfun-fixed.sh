#!/bin/bash

# Deploy the fixed HopFun contract as a new package
# This script deploys the HopFun contract with the accept_connector fix

set -e

echo "🚀 Deploying Fixed HopFun Contract..."

# Change to the hopfun contract directory
cd contracts/hopfun

# First, we need to modify the Move.toml to allow publishing as a new package
# Create a backup of the original Move.toml
cp Move.toml Move.toml.backup

# Modify the Move.toml to use 0x0 address for new deployment
sed -i.tmp 's/hopfun = "0xa531229fcef158d5f602d376d3c47abfd83f834b4459a6905053a5a8e18c563c"/hopfun = "0x0"/' Move.toml

echo "📝 Modified Move.toml for new deployment"

# Build the contract
echo "🔨 Building contract..."
sui move build

# Deploy the contract
echo "📦 Publishing contract..."
DEPLOY_OUTPUT=$(sui client publish --gas-budget 100000000 --json)

# Extract the package ID from the output
PACKAGE_ID=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.type == "published") | .packageId')
ADMIN_CAP_ID=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("AdminCap")) | .objectId')

echo "✅ Contract deployed successfully!"
echo "📦 New HopFun Package ID: $PACKAGE_ID"
echo "🔑 Admin Cap ID: $ADMIN_CAP_ID"

# Restore the original Move.toml
mv Move.toml.backup Move.toml

# Update the deployments.json file
cd ../../
echo "📝 Updating deployments.json..."

# Create a backup of deployments.json
cp config/deployments.json config/deployments.json.backup

# Update the deployments.json with new package ID
jq --arg pkg_id "$PACKAGE_ID" --arg admin_cap "$ADMIN_CAP_ID" '
  .devnet.hopfun.packageId = $pkg_id |
  .devnet.hopfun.adminCap = $admin_cap
' config/deployments.json > config/deployments.json.tmp && mv config/deployments.json.tmp config/deployments.json

echo "✅ Updated deployments.json"

# The MemeConfig is created automatically during package deployment via init function
echo "🔧 Extracting MemeConfig from deployment..."

# Extract the MemeConfig object ID from the deployment output
MEME_CONFIG_ID=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("MemeConfig")) | .objectId')

echo "✅ New MemeConfig created: $MEME_CONFIG_ID"

# Update deployments.json with the new MemeConfig ID
jq --arg config_id "$MEME_CONFIG_ID" '
  .devnet.hopfun.memeConfigId = $config_id
' config/deployments.json > config/deployments.json.tmp && mv config/deployments.json.tmp config/deployments.json

echo "📝 Final deployments.json update complete"

echo ""
echo "🎉 Fixed HopFun Contract Deployment Complete!"
echo ""
echo "📋 New Configuration:"
echo "   Package ID: $PACKAGE_ID"
echo "   MemeConfig ID: $MEME_CONFIG_ID"
echo "   Admin Cap ID: $ADMIN_CAP_ID"
echo ""
echo "⚠️  Next Steps:"
echo "1. Update the frontend network configuration with the new Package ID"
echo "2. Update the registry with the new MemeConfig address"
echo "3. Test the token creation flow"
echo ""
echo "🔧 Frontend Update Required:"
echo "   Update hopfunPackageId in network-config.service.ts to: $PACKAGE_ID"
echo "   Update memeConfigId in network-config.service.ts to: $MEME_CONFIG_ID"
