# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "35D9DB913DC665309D3DEE5E0A108A0E186B2BC9B498CEC969B85BF4E73D9113"
deps_digest = "52B406A7A21811BEF51751CF88DA0E76DAEFFEAC888D4F4060B1A72BBE7D8D35"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "config_registry", name = "config_registry" },
  { id = "hopdex", name = "hopdex" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "b448b1d971bd6c1aac8ef4eee4305943806d5d5b", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "b448b1d971bd6c1aac8ef4eee4305943806d5d5b", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "b448b1d971bd6c1aac8ef4eee4305943806d5d5b", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "b448b1d971bd6c1aac8ef4eee4305943806d5d5b", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "config_registry"
source = { local = "../config_registry" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "hopdex"
source = { local = "../hopdex" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.53.2"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "0343a9bd"
original-published-id = "0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb"
latest-published-id = "0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb"
published-version = "1"
