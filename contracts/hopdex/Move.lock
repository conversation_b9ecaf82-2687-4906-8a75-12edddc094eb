# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "35E29B67D8340F89EFCBBA1A9A5D64CBAE15A1BFA0D2B83A4DFB3CEBF7259D62"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.53.2"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "0343a9bd"
original-published-id = "0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac"
latest-published-id = "0x34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac"
published-version = "1"
