[package]
name = "coin_template"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
coin_template = "0x0"
hopfun = "0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb"
config_registry = "0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e"
