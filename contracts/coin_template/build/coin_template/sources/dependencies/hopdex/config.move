module hopdex::config {
    use hopdex::events;

    const CONTRACT_VERSION: u64 = 0;

    const E_MIN_VERSION: u64 = 0;
    const E_SWAPS_DISABLED: u64 = 1;

    public struct DexConfig has key {
        id: UID,

        protocol_share_bps: u64, // 500 - 50%
        pool_fee_rate_bps: u64, // bps

        treasury_address: address,
        
        swaps_enabled: bool,
        min_version: u64,
    }

    public struct AdminCap has key, store {
        id: UID
    }

    public struct CONFIG has drop {}

    fun init(
        _witness: CONFIG,
        ctx: &mut TxContext
    ) {
        let dex_config = DexConfig {
            id: object::new(ctx),
            protocol_share_bps: 5_000, // default 50%
            pool_fee_rate_bps: 100,       // default 1%
            treasury_address: ctx.sender(),
            swaps_enabled: true,
            min_version: CONTRACT_VERSION,
        };
        transfer::share_object(dex_config);

        let admin_cap = AdminCap {
            id: object::new(ctx)
        };
        transfer::public_transfer(admin_cap, ctx.sender());
    }

    /*
     * Public
     */

    public fun enforce_min_version(
        config: &DexConfig
    ) {
        assert!(CONTRACT_VERSION >= config.min_version, E_MIN_VERSION);
    }

    public fun enforce_swaps_enabled(config: &DexConfig) {
        assert!(config.swaps_enabled, E_SWAPS_DISABLED);
    }

    public fun protocol_share_bps(config: &DexConfig): u64 {
        config.protocol_share_bps
    }

    public fun pool_fee_rate_bps(config: &DexConfig): u64 {
        config.pool_fee_rate_bps
    }

    public fun treasury_address(config: &DexConfig): address {
        config.treasury_address
    }

    public fun swaps_enabled(config: &DexConfig): bool {
        config.swaps_enabled
    }

    public fun min_version(config: &DexConfig): u64 {
        config.min_version
    }

    /*
     * Admin
     */

    fun emit_config_event(
        config: &DexConfig
    ) {
        events::emit_update_config_event(
            config.protocol_share_bps(), 
            config.pool_fee_rate_bps(), 
            config.treasury_address(), 
            config.swaps_enabled(), 
            config.min_version()
        );
    }

    public fun set_protocol_share_bps(
        _: &AdminCap,
        config: &mut DexConfig, 
        new_bps: u64
    ) {
        config.protocol_share_bps = new_bps;
        emit_config_event(config)
    }

    public fun set_pool_fee_rate(
        _: &AdminCap,
        config: &mut DexConfig, 
        new_bps: u64
    ) {
        config.pool_fee_rate_bps = new_bps;
        emit_config_event(config)
    }

    public fun set_protocol_treasury_address(
        _: &AdminCap,
        config: &mut DexConfig,
        treasury_address: address
    ) {
        config.treasury_address = treasury_address;
        emit_config_event(config);
    }

    public fun set_swaps_enabled(
        _: &AdminCap,
        config: &mut DexConfig,
        enabled: bool
    ) {
        config.swaps_enabled = enabled;
        emit_config_event(config);
    }

    public fun set_min_version(
        _: &AdminCap,
        config: &mut DexConfig,
        version: u64
    ) {
        config.min_version = version;
        emit_config_event(config);
    }

}