module hopfun::connector {
    use std::string;
    use sui::coin::{Self, TreasuryCap, CoinMetadata};
    use sui::balance::Balance;
    use hopfun::events;
    use config_registry::registry::{Self, ConfigRegistry};

    const TOKEN_DECIMALS: u8 = 6;
    const ERegistryNotInitialized: u64 = 100;

    public struct Connector<phantom T> has key, store {
        id: UID,
        temp_id: u64,
        supply: Balance<T>,
        twitter: string::String,
        website: string::String,
        telegram: string::String,
        creator: address
    }

    /// Creates a new Connector and transfers it to the MemeConfig address from registry
    public fun new<T: drop>(
        witness: T,
        temp_id: u64,
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: vector<u8>,
        twitter: string::String,
        website: string::String,
        telegram: string::String,
        total_supply: u64,
        registry: &ConfigRegistry,  // Pass the shared registry object
        ctx: &mut TxContext,
    ) {
        // Check that registry is initialized
        assert!(registry::is_meme_config_initialized(registry), ERegistryNotInitialized);

        let (mut treasury_cap, metadata) = coin::create_currency<T>(
            witness, TOKEN_DECIMALS, symbol, name, description,
            option::some(sui::url::new_unsafe_from_bytes(icon_url)), ctx
        );
        transfer::public_freeze_object<CoinMetadata<T>>(metadata);

        let creator = tx_context::sender(ctx);
        let supply = treasury_cap.mint(total_supply, ctx);

        let connector = Connector<T> {
            temp_id,
            id: object::new(ctx),
            supply: supply.into_balance(),
            twitter,
            website,
            telegram,
            creator
        };
        events::emit_connector_create<T>(connector.get_id());

        transfer::public_freeze_object<TreasuryCap<T>>(treasury_cap);

        // Get MemeConfig address from registry and transfer
        let meme_config_address = registry::get_meme_config_address(registry);
        transfer::public_transfer(connector, meme_config_address);
    }

    // ======== Accessor Functions ========
    public fun get_temp_id<T>(connector: &Connector<T>): u64 {
        connector.temp_id
    }

    public fun get_creator<T>(connector: &Connector<T>): address {
        connector.creator
    }

    public fun get_id<T>(connector: &Connector<T>): object::ID {
        object::id(connector)
    }

    public fun get_twitter<T>(connector: &Connector<T>): &string::String {
        &connector.twitter
    }

    public fun get_website<T>(connector: &Connector<T>): &string::String {
        &connector.website
    }

    public fun get_telegram<T>(connector: &Connector<T>): &string::String {
        &connector.telegram
    }

    public fun get_supply<T>(connector: &Connector<T>): u64 {
        connector.supply.value()
    }

    // ======== Mutable Functions ========
    public(package) fun extract_supply<T>(connector: &mut Connector<T>, amount: u64): Balance<T> {
        connector.supply.split(amount)
    }

    public(package) fun add_supply<T>(connector: &mut Connector<T>, balance: Balance<T>) {
        connector.supply.join(balance);
    }

    public(package) fun destroy_empty<T>(connector: Connector<T>) {
        let Connector {
            id,
            temp_id: _,
            supply,
            twitter: _,
            website: _,
            telegram: _,
            creator: _
        } = connector;

        supply.destroy_zero();
        object::delete(id);
    }

    /// Deconstruct a Connector and return all its fields without side effects
    public(package) fun deconstruct<T>(connector: Connector<T>): (UID, Balance<T>, string::String, string::String, string::String, address) {
        let Connector {
            id,
            temp_id: _,
            supply,
            twitter,
            website,
            telegram,
            creator
        } = connector;
        (id, supply, twitter, website, telegram, creator)
    }

    /// Creates a new Connector from existing supply (for use when currency is already created)
    public fun new_from_supply<T>(
        temp_id: u64,
        supply: Balance<T>,
        twitter: string::String,
        website: string::String,
        telegram: string::String,
        creator: address,
        registry: &ConfigRegistry,
        ctx: &mut TxContext,
    ) {
        // Check that registry is initialized
        assert!(registry::is_meme_config_initialized(registry), ERegistryNotInitialized);

        let connector = Connector<T> {
            temp_id,
            id: object::new(ctx),
            supply,
            twitter,
            website,
            telegram,
            creator
        };
        events::emit_connector_create<T>(object::id(&connector));

        // Get MemeConfig address from registry and transfer
        let meme_config_address = registry::get_meme_config_address(registry);
        transfer::public_transfer(connector, meme_config_address);
    }

}