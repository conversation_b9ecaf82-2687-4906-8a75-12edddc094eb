{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/bridge/sources/message_types.move", "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 90, "end": 103}, "module_name": ["000000000000000000000000000000000000000000000000000000000000000b", "message_types"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 310, "end": 342}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 321, "end": 326}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 330, "end": 332}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 335, "end": 340}}, "is_native": false}, "1": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 344, "end": 404}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 355, "end": 374}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 378, "end": 380}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 383, "end": 402}}, "is_native": false}, "2": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 406, "end": 452}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 417, "end": 429}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 433, "end": 435}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 438, "end": 450}}, "is_native": false}, "3": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 454, "end": 514}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 465, "end": 484}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 488, "end": 490}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 493, "end": 512}}, "is_native": false}, "4": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 516, "end": 574}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 527, "end": 545}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 549, "end": 551}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 554, "end": 572}}, "is_native": false}, "5": {"location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 576, "end": 632}, "definition_location": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 587, "end": 604}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 608, "end": 610}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [74, 20, 107, 224, 210, 124, 149, 95, 253, 147, 20, 91, 44, 132, 77, 101, 173, 110, 139, 159, 219, 87, 126, 30, 125, 149, 86, 240, 52, 122, 112, 229], "start": 613, "end": 630}}, "is_native": false}}, "constant_map": {"ADD_TOKENS_ON_SUI": 5, "COMMITTEE_BLOCKLIST": 1, "EMERGENCY_OP": 2, "TOKEN": 0, "UPDATE_ASSET_PRICE": 4, "UPDATE_BRIDGE_LIMIT": 3}}