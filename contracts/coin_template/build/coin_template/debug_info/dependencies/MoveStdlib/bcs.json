{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/move-stdlib/sources/bcs.move", "definition_location": {"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 395, "end": 398}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "bcs"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 492, "end": 557}, "definition_location": {"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 510, "end": 518}, "type_parameters": [["MoveValue", {"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 519, "end": 528}]], "parameters": [["v#0#0", {"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 530, "end": 531}]], "returns": [{"file_hash": [34, 201, 103, 208, 120, 108, 208, 171, 127, 162, 154, 113, 96, 186, 51, 169, 173, 216, 199, 217, 88, 54, 128, 150, 101, 140, 27, 7, 37, 201, 47, 24], "start": 546, "end": 556}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}