{"version": 2, "from_file_path": "/Users/<USER>/Programming/hopaggregator/hopfun/contracts/hopdex/sources/math.move", "definition_location": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 15, "end": 19}, "module_name": ["34e73a48dce5c568abbea7b9daccd4791520e80a246a772c1cd4c969e20216ac", "math"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 27, "end": 427}, "definition_location": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 38, "end": 58}, "type_parameters": [], "parameters": [["amount_in#0#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 68, "end": 77}], ["reserve_in#0#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 92, "end": 102}], ["reserve_out#0#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 117, "end": 128}]], "returns": [{"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 142, "end": 145}], "locals": [["amount_in_with_fee#1#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 202, "end": 220}], ["denominator#1#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 324, "end": 335}], ["numerator#1#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 256, "end": 265}]], "nops": {}, "code_map": {"0": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 224, "end": 233}, "1": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 224, "end": 241}, "2": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 202, "end": 220}, "3": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 268, "end": 286}, "4": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 290, "end": 301}, "5": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 290, "end": 309}, "6": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 287, "end": 288}, "7": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 256, "end": 265}, "8": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 339, "end": 349}, "9": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 339, "end": 357}, "10": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 361, "end": 379}, "11": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 359, "end": 360}, "12": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 324, "end": 335}, "13": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 390, "end": 399}, "14": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 402, "end": 413}, "15": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 400, "end": 401}, "16": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 389, "end": 421}}, "is_native": false}, "1": {"location": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 433, "end": 598}, "definition_location": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 444, "end": 452}, "type_parameters": [], "parameters": [["amount#0#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 462, "end": 468}], ["fee_rate_bps#0#0", {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 483, "end": 495}]], "returns": [{"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 509, "end": 512}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 525, "end": 531}, "1": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 525, "end": 539}, "2": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 544, "end": 556}, "3": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 544, "end": 564}, "4": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 541, "end": 542}, "5": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 569, "end": 583}, "6": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 566, "end": 567}, "7": {"file_hash": [233, 147, 115, 15, 155, 28, 37, 123, 64, 142, 33, 120, 224, 98, 131, 1, 168, 240, 77, 185, 116, 221, 121, 231, 197, 7, 135, 55, 152, 21, 47, 65], "start": 523, "end": 592}}, "is_native": false}}, "constant_map": {}}