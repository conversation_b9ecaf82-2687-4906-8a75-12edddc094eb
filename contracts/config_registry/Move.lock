# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "08AE5250891188A6FB9747237C74060B812D0F4137F526E0013B3F6A013BA363"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.53.2"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "0343a9bd"
original-published-id = "0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e"
latest-published-id = "0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e"
published-version = "1"
