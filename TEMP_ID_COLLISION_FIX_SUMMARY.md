# temp_id Collision Fix Summary

## Problem
The third approval request transaction was failing with the error:
```
Dry run failed, could not automatically determine a budget: MoveAbort(MoveLocation { module: ModuleId { address: 0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb, name: Identifier("meme") }, function: 10, instruction: 19, function_name: Some("place_dev_order") }, 3) in command 1 (code: -1)
```

## Root Cause Analysis
The issue was identified as **temp_id collision (EUniqueIdRequired - error code 3)**:

### Technical Details:
1. **Error Location**: `place_dev_order` function, line 387
2. **Error Code 3**: `EUniqueIdRequired` - temp_id already exists
3. **Assertion**: `assert!(!df::exists_<u64>(config.id(), temp_id), EUniqueIdRequired);`
4. **Root Cause**: Hardcoded temp_id values causing collisions

### The Problem:
- **<PERSON><PERSON> used hardcoded temp_id (123)** in multiple places
- **All users and retry attempts used the same temp_id**
- **MemeConfig dynamic fields already contained temp_id 123**
- **place_dev_order requires unique temp_id for each token creation**

## Solution Implemented

### 1. Unique temp_id Generation
**File**: `apps/frontend/src/services/token-creation.service.ts`

**New Function**:
```typescript
static generateUniqueTempId(userAddress?: string): number {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000000);
  
  // Use user address for additional entropy if available
  let userSuffix = 0;
  if (userAddress) {
    const addressSuffix = userAddress.slice(-8);
    userSuffix = parseInt(addressSuffix, 16) % 1000000;
  } else {
    userSuffix = Math.floor(Math.random() * 1000000);
  }
  
  // Combine timestamp, random, and user suffix
  const tempId = ((timestamp % 1000000000) * 1000000 + random + userSuffix) % Number.MAX_SAFE_INTEGER;
  
  return tempId;
}
```

**Entropy Sources**:
- **Timestamp**: `Date.now()` for time-based uniqueness
- **Random Number**: `Math.random()` for additional randomness
- **User Address**: Last 8 characters for user-specific entropy

### 2. Collision Detection and Retry Logic
**New Functions**:
```typescript
static isTempIdCollisionError(error: any): boolean {
  const errorMessage = error?.message || error?.toString() || '';
  return errorMessage.includes('MoveAbort') && errorMessage.includes(', 3)');
}

static async executeWithTempIdRetry(
  signAndExecuteTransaction: any,
  createTransactionFn: (tempId: number) => any,
  maxRetries = 3,
  userAddress?: string,
): Promise<{ result: any; tempId: number }>
```

**Retry Logic**:
- **Detects EUniqueIdRequired errors** (MoveAbort code 3)
- **Automatically generates new temp_id** on collision
- **Exponential backoff** between retry attempts
- **Maximum 3 retry attempts** before failing

### 3. Updated Token Creation Flow
**Before**:
```typescript
// Hardcoded temp_id
const tempId = 123;
const placeOrderTx = this.createPlaceDevOrderTransaction(packageId, tempId, 0);
const result = await signAndExecuteTransaction({ transaction: placeOrderTx });
```

**After**:
```typescript
// Dynamic temp_id with retry logic
const { result: placeOrderResult, tempId } = await this.executeWithTempIdRetry(
  signAndExecuteTransaction,
  (tempId: number) => this.createPlaceDevOrderTransaction(packageId, tempId, 0),
  3, // Max retries
);
```

### 4. Removed Hardcoded Values
**Updated Locations**:
- Line 305: `tempId = 123` → Removed default
- Line 341: `tempId = 123` → Removed default  
- Line 665: `123, // TEMP_ID` → Uses generated `tempId`
- Line 728: `123, // TEMP_ID` → Uses same `tempId` from place_dev_order

## Verification Results

### Test Results:
✅ **Uniqueness**: Generated 100 unique temp_ids successfully
✅ **Error Detection**: Correctly identifies EUniqueIdRequired errors
✅ **Retry Logic**: Properly handles collision scenarios
✅ **Transaction Structure**: Valid with new temp_id generation
✅ **Existing Collision**: Confirmed temp_id 123 exists in MemeConfig

### Example Generated temp_ids:
- `601046468707585` (timestamp + random + entropy)
- Each generation produces different values
- No collisions in 100+ test generations

## How The Fix Works

### Before Fix:
1. **All users use temp_id 123**
2. **First user succeeds, temp_id 123 stored in MemeConfig**
3. **Subsequent users/retries fail with EUniqueIdRequired**
4. **No retry mechanism** ❌

### After Fix:
1. **Each attempt generates unique temp_id** (e.g., `601046468707585`)
2. **If collision occurs (rare), automatically retries with new temp_id**
3. **High entropy ensures uniqueness across users and time**
4. **Automatic retry handles edge cases** ✅

## Expected Outcome

### For Token Creation:
1. **Publish coin module** ✅ (unchanged)
2. **Create connector** ✅ (unchanged)
3. **Place dev order** ✅ (NOW FIXED - uses unique temp_id with retry)
4. **Accept connector** ✅ (uses same temp_id from successful place_dev_order)

### Benefits:
- **No more temp_id collisions** for new token creations
- **Automatic retry** on rare collision scenarios
- **Improved reliability** and user experience
- **Scalable solution** for multiple concurrent users

## Files Modified
1. `apps/frontend/src/services/token-creation.service.ts` - Added unique temp_id generation and retry logic
2. Removed all hardcoded temp_id values (123)
3. Updated token creation flow to use dynamic temp_id generation

## Testing
- ✅ Comprehensive test suite validates all functionality
- ✅ Uniqueness verified across 100+ generations
- ✅ Error detection and retry logic tested
- ✅ Transaction structure validated with new temp_ids

## Next Steps
🚀 **The temp_id collision error should now be resolved!**

**Try creating a new token through the UI:**
1. **Third transaction (place_dev_order) should succeed** with unique temp_id
2. **Automatic retry** will handle any rare collisions
3. **Complete token creation flow** should work reliably
4. **Multiple users** can create tokens simultaneously without conflicts

The fix ensures that each token creation attempt uses a unique temp_id, eliminating the collision issue while providing robust retry mechanisms for edge cases.
