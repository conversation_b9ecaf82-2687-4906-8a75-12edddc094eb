{
  "status": "error",
  "message": "An error occurred, please try again later",
  "error": {
    "code": "COMPILATION_FAILED",
    "message": "Contract compilation failed: Compilation failed: Build failed: Command failed: sui move build --path /Users/<USER>/Programming/hopaggregator/hopfun/apps/server/temp/compilations/e6e2f25e1aed5943f47de9f32a05ffa9\n[note] Dependencies on Bridge, MoveStdlib, Sui, and SuiSystem are automatically added, but this feature is disabled for your package because you have explicitly included dependencies on MoveStdlib and Sui. Consider removing these dependencies from Move.toml.\nUPDATING GIT DEPENDENCY https://github.com/MystenLabs/sui.git\nINCLUDING DEPENDENCY Sui\nINCLUDING DEPENDENCY MoveStdlib\nBUILDING DynamicCoin\n[0m[1m[38;5;11mwarning[W02021][0m[1m: duplicate alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:2:14\n  [0m[34m│[0m\n[0m[34m2[0m [0m[34m│[0m     use std::[0m[33moption[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^[0m [0m[33mUnnecessary alias 'option' for module 'std::option'. This alias is provided by default[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:3:14\n  [0m[34m│[0m\n[0m[34m3[0m [0m[34m│[0m     use std::[0m[33mstring[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^[0m [0m[33mUnused 'use' of alias 'string'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:4:14\n  [0m[34m│[0m\n[0m[34m4[0m [0m[34m│[0m     use std::[0m[33mtype_name[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^^^^[0m [0m[33mUnused 'use' of alias 'type_name'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:7:14\n  [0m[34m│[0m\n[0m[34m7[0m [0m[34m│[0m     use sui::[0m[33mevent[0m;\n  [0m[34m│[0m              [0m[33m^^^^^[0m [0m[33mUnused 'use' of alias 'event'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n    [0m[34m┌─[0m ./sources/test1_5d3ae750.move:136:20\n    [0m[34m│[0m\n[0m[34m136[0m [0m[34m│[0m         registry: &[0m[31m0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e::registry[0m::ConfigRegistry,\n    [0m[34m│[0m                    [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mUnbound module '0x466228B4706A6B1F7E493F67EFD29DEECC781616F96CB4A742407EA77B9AFB7E::registry'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n    [0m[34m┌─[0m ./sources/test1_5d3ae750.move:153:9\n    [0m[34m│[0m\n[0m[34m153[0m [0m[34m│[0m         [0m[31m0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb::connector[0m::new_from_supply<COIN>(\n    [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mUnbound module '0x701158BF741C82CB75CE84E9BF9DF93E4D8775712824A961E20C7D309AB5CDB::connector'[0m\n\n",
    "details": {
      "compilationId": "959c53a16cc6d19a2daa88d605a1db97",
      "originalError": "Compilation failed: Build failed: Command failed: sui move build --path /Users/<USER>/Programming/hopaggregator/hopfun/apps/server/temp/compilations/e6e2f25e1aed5943f47de9f32a05ffa9\n[note] Dependencies on Bridge, MoveStdlib, Sui, and SuiSystem are automatically added, but this feature is disabled for your package because you have explicitly included dependencies on MoveStdlib and Sui. Consider removing these dependencies from Move.toml.\nUPDATING GIT DEPENDENCY https://github.com/MystenLabs/sui.git\nINCLUDING DEPENDENCY Sui\nINCLUDING DEPENDENCY MoveStdlib\nBUILDING DynamicCoin\n[0m[1m[38;5;11mwarning[W02021][0m[1m: duplicate alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:2:14\n  [0m[34m│[0m\n[0m[34m2[0m [0m[34m│[0m     use std::[0m[33moption[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^[0m [0m[33mUnnecessary alias 'option' for module 'std::option'. This alias is provided by default[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:3:14\n  [0m[34m│[0m\n[0m[34m3[0m [0m[34m│[0m     use std::[0m[33mstring[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^[0m [0m[33mUnused 'use' of alias 'string'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:4:14\n  [0m[34m│[0m\n[0m[34m4[0m [0m[34m│[0m     use std::[0m[33mtype_name[0m;\n  [0m[34m│[0m              [0m[33m^^^^^^^^^[0m [0m[33mUnused 'use' of alias 'type_name'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;11mwarning[W09001][0m[1m: unused alias[0m\n  [0m[34m┌─[0m ./sources/test1_5d3ae750.move:7:14\n  [0m[34m│[0m\n[0m[34m7[0m [0m[34m│[0m     use sui::[0m[33mevent[0m;\n  [0m[34m│[0m              [0m[33m^^^^^[0m [0m[33mUnused 'use' of alias 'event'. Consider removing it[0m\n  [0m[34m│[0m\n  [0m[34m=[0m This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n    [0m[34m┌─[0m ./sources/test1_5d3ae750.move:136:20\n    [0m[34m│[0m\n[0m[34m136[0m [0m[34m│[0m         registry: &[0m[31m0x466228b4706a6b1f7e493f67efd29deecc781616f96cb4a742407ea77b9afb7e::registry[0m::ConfigRegistry,\n    [0m[34m│[0m                    [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mUnbound module '0x466228B4706A6B1F7E493F67EFD29DEECC781616F96CB4A742407EA77B9AFB7E::registry'[0m\n\n[0m[1m[38;5;9merror[E03002][0m[1m: unbound module[0m\n    [0m[34m┌─[0m ./sources/test1_5d3ae750.move:153:9\n    [0m[34m│[0m\n[0m[34m153[0m [0m[34m│[0m         [0m[31m0x0701158bf741c82cb75ce84e9bf9df93e4d8775712824a961e20c7d309ab5cdb::connector[0m::new_from_supply<COIN>(\n    [0m[34m│[0m         [0m[31m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m [0m[31mUnbound module '0x701158BF741C82CB75CE84E9BF9DF93E4D8775712824A961E20C7D309AB5CDB::connector'[0m\n\n",
      "duration": 7742
    }
  }
}
